// 简单的状态管理 store
import { useState } from 'react';

// 用户状态接口
interface UserState {
  initDone: boolean;
  permissionButtons: Array<{ permissionCode: string }>;
}

// 字典状态接口
interface DictState {
  dictMap: { [key: string]: any };
}

// 字典操作接口
interface DictDispatchers {
  getDictList: (params: any) => Promise<void>;
  updateDictMap: (data: any) => void;
}

// 模拟 store 的 useModel hook
const useModel = (modelName: string) => {
  const [userState] = useState<UserState>({
    initDone: true,
    permissionButtons: [],
  });

  const [dictState] = useState<DictState>({
    dictMap: {},
  });

  const dictDispatchers: DictDispatchers = {
    getDictList: async (params: any) => {
      // 模拟异步获取字典数据
      console.log('Getting dict list:', params);
    },
    updateDictMap: (data: any) => {
      // 模拟更新字典数据
      console.log('Updating dict map:', data);
    },
  };

  if (modelName === 'user') {
    return [userState];
  }

  if (modelName === 'dict') {
    return [dictState, dictDispatchers];
  }

  return [{}];
};

const store = {
  useModel,
};

export default store;
