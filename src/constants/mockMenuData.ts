export default {
  header: {
    requestId: '99999999',
    gid: '888888',
    errorCode: '000000',
    errorMsg: 'SUCCESS',
    success: true,
  },
  data: {
    resourceList: [
      // 工作台
      {
        id: 1,
        displayOrder: 1,
        identifierField: 'id',
        resourceName: '工作台',
        resourceUrl: '/workBench/messageBench',
        resourceDesc: '工作台',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        children: [
          {
            parentId: 1,
            id: 1001,
            displayOrder: 1,
            identifierField: 'id',
            resourceName: '管理员工作台',
            resourceUrl: '/workBench/messageBench',
            resourceDesc: '管理员工作台',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            permissions: [
              { permissionId: 'create', permissionName: '新增' },
              { permissionId: 'edit', permissionName: '编辑' },
              { permissionId: 'delete', permissionName: '删除' },
            ],
          },
          {
            parentId: 1,
            id: 1002,
            displayOrder: 2,
            identifierField: 'id',
            resourceName: '催收员工作台',
            resourceUrl: '/workBench/collectorBench',
            resourceDesc: '催收员工作台',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            permissions: [
              { permissionId: 'create', permissionName: '新增' },
              { permissionId: 'edit', permissionName: '编辑' },
              { permissionId: 'delete', permissionName: '删除' },
            ],
          },
        ],
      },
      // 案件管理
      {
        id: 2,
        displayOrder: 2,
        identifierField: 'id',
        resourceName: '案件管理',
        resourceUrl: '/caseManage/caseQuery',
        resourceDesc: '案件管理',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        children: [
          {
            parentId: 2,
            id: 2001,
            displayOrder: 1,
            identifierField: 'id',
            resourceName: '案件查询',
            resourceUrl: '/caseManage/caseQuery',
            resourceDesc: '案件查询',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            permissions: [
              { permissionId: 'create', permissionName: '新增' },
              { permissionId: 'edit', permissionName: '编辑' },
              { permissionId: 'delete', permissionName: '删除' },
            ],
          },
          {
            parentId: 2,
            id: 2002,
            displayOrder: 2,
            identifierField: 'id',
            resourceName: '案件列表',
            resourceUrl: '/caseManage/caseList',
            resourceDesc: '案件列表',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            permissions: [
              { permissionId: 'create', permissionName: '新增' },
              { permissionId: 'edit', permissionName: '编辑' },
              { permissionId: 'delete', permissionName: '删除' },
            ],
          },
          {
            parentId: 2,
            id: 2003,
            displayOrder: 3,
            identifierField: 'id',
            resourceName: '核心查询',
            resourceUrl: '/caseManage/cardQuery',
            resourceDesc: '核心查询',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            permissions: [
              { permissionId: 'create', permissionName: '新增' },
              { permissionId: 'edit', permissionName: '编辑' },
              { permissionId: 'delete', permissionName: '删除' },
            ],
          },
          {
            parentId: 2,
            id: 2004,
            displayOrder: 4,
            identifierField: 'id',
            resourceName: '案件审批',
            resourceUrl: '/systemManage/aduitManage',
            resourceDesc: '案件审批',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            permissions: [
              { permissionId: 'create', permissionName: '新增' },
              { permissionId: 'edit', permissionName: '编辑' },
              { permissionId: 'delete', permissionName: '删除' },
            ],
          },
        ],
      },
      // 策略管理
      {
        id: 3,
        displayOrder: 3,
        identifierField: 'id',
        resourceName: '策略管理',
        resourceUrl: '/strategyManage/strategySetting',
        resourceDesc: '策略管理',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        children: [
          {
            parentId: 3,
            id: 3001,
            displayOrder: 1,
            identifierField: 'id',
            resourceName: '派案策略管理',
            resourceUrl: '/strategyManage/strategySetting',
            resourceDesc: '派案策略管理',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            permissions: [
              { permissionId: 'create', permissionName: '新增' },
              { permissionId: 'edit', permissionName: '编辑' },
              { permissionId: 'delete', permissionName: '删除' },
            ],
          },
          {
            parentId: 3,
            id: 3002,
            displayOrder: 3,
            identifierField: 'id',
            resourceName: '权重策略管理',
            resourceUrl: '/strategyManage/weightSetting',
            resourceDesc: '权重策略管理',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            permissions: [
              { permissionId: 'create', permissionName: '新增' },
              { permissionId: 'edit', permissionName: '编辑' },
              { permissionId: 'delete', permissionName: '删除' },
            ],
          },
          {
            parentId: 3,
            id: 3003,
            displayOrder: 2,
            identifierField: 'id',
            resourceName: '排序模板管理',
            resourceUrl: '/strategyManage/sortSetting',
            resourceDesc: '排序模板管理',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            permissions: [
              { permissionId: 'create', permissionName: '新增' },
              { permissionId: 'edit', permissionName: '编辑' },
              { permissionId: 'delete', permissionName: '删除' },
            ],
          },
          {
            parentId: 3,
            id: 3004,
            displayOrder: 4,
            identifierField: 'id',
            resourceName: '工作台管理',
            resourceUrl: '/strategyManage/workbenck',
            resourceDesc: '工作台管理',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            permissions: [
              { permissionId: 'create', permissionName: '新增' },
              { permissionId: 'edit', permissionName: '编辑' },
              { permissionId: 'delete', permissionName: '删除' },
            ],
          },
        ],
      },
      // 流程管理
      {
        id: 4,
        displayOrder: 4,
        identifierField: 'id',
        resourceName: '流程管理',
        resourceUrl: '/flowManage/nodeConfig',
        resourceDesc: '流程管理',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        children: [
          {
            parentId: 4,
            id: 4001,
            displayOrder: 1,
            identifierField: 'id',
            resourceName: '节点管理',
            resourceUrl: '/flowManage/nodeConfig',
            resourceDesc: '节点管理',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            permissions: [
              { permissionId: 'create', permissionName: '新增' },
              { permissionId: 'edit', permissionName: '编辑' },
              { permissionId: 'delete', permissionName: '删除' },
            ],
          },
          {
            parentId: 4,
            id: 4002,
            displayOrder: 2,
            identifierField: 'id',
            resourceName: '处理程序管理',
            resourceUrl: '/flowManage/program',
            resourceDesc: '处理程序管理',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            permissions: [
              { permissionId: 'create', permissionName: '新增' },
              { permissionId: 'edit', permissionName: '编辑' },
              { permissionId: 'delete', permissionName: '删除' },
            ],
          },
          {
            parentId: 4,
            id: 4003,
            displayOrder: 3,
            identifierField: 'id',
            resourceName: '标签管理',
            resourceUrl: '/flowManage/labelManage',
            resourceDesc: '标签管理',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            permissions: [
              { permissionId: 'create', permissionName: '新增' },
              { permissionId: 'edit', permissionName: '编辑' },
              { permissionId: 'delete', permissionName: '删除' },
            ],
          },
          {
            parentId: 4,
            id: 4004,
            displayOrder: 4,
            identifierField: 'id',
            resourceName: '规则管理',
            resourceUrl: '',
            resourceDesc: '规则管理',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            children: [
              {
                parentId: 4004,
                id: 400401,
                displayOrder: 1,
                identifierField: 'id',
                resourceName: '规则因子',
                resourceUrl: '/operationCenter/rules/ruleFactor',
                resourceDesc: '规则因子',
                createTime: '2024-6-20 12:12:12',
                updateTime: '2024-6-21 13:13:13',
                createUser: 'testUser1',
                updateUser: 'testUser2',
                permissions: [
                  { permissionId: 'create', permissionName: '新增' },
                  { permissionId: 'edit', permissionName: '编辑' },
                  { permissionId: 'delete', permissionName: '删除' },
                ],
              },
              {
                parentId: 4004,
                id: 400402,
                displayOrder: 2,
                identifierField: 'id',
                resourceName: '规则管理',
                resourceUrl: '/operationCenter/rules/rulesConfig',
                resourceDesc: '规则管理',
                createTime: '2024-6-20 12:12:12',
                updateTime: '2024-6-21 13:13:13',
                createUser: 'testUser1',
                updateUser: 'testUser2',
                permissions: [
                  { permissionId: 'create', permissionName: '新增' },
                  { permissionId: 'edit', permissionName: '编辑' },
                  { permissionId: 'delete', permissionName: '删除' },
                ],
              },
            ],
          },
        ],
      },
      // 运营管理
      {
        id: 5,
        displayOrder: 5,
        identifierField: 'id',
        resourceName: '运营管理',
        resourceUrl: '',
        resourceDesc: '运营管理',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        children: [
          {
            parentId: 5,
            id: 5001,
            displayOrder: 1,
            identifierField: 'id',
            resourceName: 'XXX监控',
            resourceUrl: '',
            resourceDesc: 'XXX监控',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            permissions: [
              { permissionId: 'create', permissionName: '新增' },
              { permissionId: 'edit', permissionName: '编辑' },
              { permissionId: 'delete', permissionName: '删除' },
            ],
          },
        ],
      },
      // 数据管理
      {
        id: 6,
        displayOrder: 6,
        identifierField: 'id',
        resourceName: '数据管理',
        resourceUrl: '/dataManage/realtime',
        resourceDesc: '数据管理',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        children: [
          {
            parentId: 6,
            id: 6001,
            displayOrder: 1,
            identifierField: 'id',
            resourceName: '实时数据流',
            resourceUrl: '/dataManage/realtime',
            resourceDesc: '实时数据流',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            permissions: [
              { permissionId: 'create', permissionName: '新增' },
              { permissionId: 'edit', permissionName: '编辑' },
              { permissionId: 'delete', permissionName: '删除' },
            ],
          },
          {
            parentId: 6,
            id: 6002,
            displayOrder: 2,
            identifierField: 'id',
            resourceName: '离线数据管理',
            resourceUrl: '/dataManage/offline',
            resourceDesc: '离线数据管理',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            permissions: [
              { permissionId: 'create', permissionName: '新增' },
              { permissionId: 'edit', permissionName: '编辑' },
              { permissionId: 'delete', permissionName: '删除' },
            ],
          },
          {
            parentId: 6,
            id: 6003,
            displayOrder: 3,
            identifierField: 'id',
            resourceName: '名单管理',
            resourceUrl: '/dataManage/callList',
            resourceDesc: '名单管理',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            permissions: [
              { permissionId: 'create', permissionName: '新增' },
              { permissionId: 'edit', permissionName: '编辑' },
              { permissionId: 'delete', permissionName: '删除' },
            ],
          },
          {
            parentId: 6,
            id: 6004,
            displayOrder: 4,
            identifierField: 'id',
            resourceName: '短信模版管理',
            resourceUrl: '/dataManage/smsTemplate',
            resourceDesc: '短信模版管理',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            permissions: [
              { permissionId: 'create', permissionName: '新增' },
              { permissionId: 'edit', permissionName: '编辑' },
              { permissionId: 'delete', permissionName: '删除' },
            ],
          },
        ],
      },
      // 分析中心
      {
        id: 7,
        displayOrder: 7,
        identifierField: 'id',
        resourceName: '分析中心',
        resourceUrl: '',
        resourceDesc: '分析中心',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        children: [
          {
            parentId: 7,
            id: 7001,
            displayOrder: 1,
            identifierField: 'id',
            resourceName: '客户洞察',
            resourceUrl: '',
            resourceDesc: '客户洞察',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            permissions: [
              { permissionId: 'create', permissionName: '新增' },
              { permissionId: 'edit', permissionName: '编辑' },
              { permissionId: 'delete', permissionName: '删除' },
            ],
          },
        ],
      },
      // 系统管理
      {
        id: 8,
        displayOrder: 8,
        identifierField: 'id',
        resourceName: '系统管理',
        resourceUrl: '/systemManage/menu',
        resourceDesc: '系统管理',
        createTime: '2024-6-20 12:12:12',
        updateTime: '2024-6-21 13:13:13',
        createUser: 'testUser1',
        updateUser: 'testUser2',
        children: [
          {
            parentId: 8,
            id: 8001,
            displayOrder: 1,
            identifierField: 'id',
            resourceName: '菜单管理',
            resourceUrl: '/systemManage/menu',
            resourceDesc: '菜单管理',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            permissions: [
              { permissionId: 'create', permissionName: '新增' },
              { permissionId: 'edit', permissionName: '编辑' },
              { permissionId: 'delete', permissionName: '删除' },
            ],
          },
          {
            parentId: 8,
            id: 8002,
            displayOrder: 2,
            identifierField: 'id',
            resourceName: '数据字典',
            resourceUrl: '/systemManage/dict',
            resourceDesc: '数据字典',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            permissions: [
              { permissionId: 'create', permissionName: '新增' },
              { permissionId: 'edit', permissionName: '编辑' },
              { permissionId: 'delete', permissionName: '删除' },
            ],
          },
          {
            parentId: 8,
            id: 8003,
            displayOrder: 3,
            identifierField: 'id',
            resourceName: '机构管理',
            resourceUrl: '/systemManage/org',
            resourceDesc: '机构管理',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            permissions: [
              { permissionId: 'create', permissionName: '新增' },
              { permissionId: 'edit', permissionName: '编辑' },
              { permissionId: 'delete', permissionName: '删除' },
            ],
          },
          {
            parentId: 8,
            id: 8004,
            displayOrder: 4,
            identifierField: 'id',
            resourceName: '岗位管理',
            resourceUrl: '/systemManage/position',
            resourceDesc: '岗位管理',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            permissions: [
              { permissionId: 'create', permissionName: '新增' },
              { permissionId: 'edit', permissionName: '编辑' },
              { permissionId: 'delete', permissionName: '删除' },
            ],
          },
          {
            parentId: 8,
            id: 8005,
            displayOrder: 5,
            identifierField: 'id',
            resourceName: '角色管理',
            resourceUrl: '/systemManage/role',
            resourceDesc: '角色管理',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            permissions: [
              { permissionId: 'create', permissionName: '新增' },
              { permissionId: 'edit', permissionName: '编辑' },
              { permissionId: 'delete', permissionName: '删除' },
            ],
          },
          {
            parentId: 8,
            id: 8006,
            displayOrder: 6,
            identifierField: 'id',
            resourceName: '用户管理',
            resourceUrl: '/systemManage/user',
            resourceDesc: '用户管理',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            createUser: 'testUser1',
            updateUser: 'testUser2',
            permissions: [
              { permissionId: 'create', permissionName: '新增' },
              { permissionId: 'edit', permissionName: '编辑' },
              { permissionId: 'delete', permissionName: '删除' },
            ],
          },
        ],
      },
    ],
  },
};
