/**
 * Created by ca<PERSON>un on 2025/6/6.
 */

import _ from 'lodash';
import React, { forwardRef, useImperativeHandle } from 'react';
import { Form, Skeleton } from 'antd';
import styleConstant from '@/constants/styleConstant';

import { useForm } from 'antd/es/form/Form';
import { IFormTemplateProps } from './types/IFormTemplate';
import useFormItem from './hooks/useFormItem';
import FormMaintenance from './FormMaintenance';

const FormTemplate: React.FC<IFormTemplateProps> = forwardRef(
  (
    {
      formRef, // 表单dom节点
      id, // form组件的id
      config, // 配置数据
      initialData = {}, // 绑定数据
      loading = false, // 是否加载
      canEdit = true, // 是否可以编辑
      showMaintenance = true, // 是否显示维护信息
      className = '', // 样式类名，比如 app-overflow
      formLayout = styleConstant.formLayout2, // 样式布局
      intlPrefix,
      editTableRefList = {},
      colSpan = 11,
      onChange = () => {},
      onExpand = () => {},
      onCheck = () => {},
      onLoad = () => {},
      onSelect = () => {},
      onSearch = () => {},
    },
    ref,
  ) => {
    // hooks变量
    const [form] = useForm();
    const { renderFormItem } = useFormItem({
      form,
      canEdit,
      intlPrefix,
      colSpan,
      onChange,
      onExpand,
      onCheck,
      onLoad,
      onSelect,
      onSearch,
    });
    // 把当前form显示暴露给父组件
    useImperativeHandle(ref, () => {
      return {
        async onSubmit() {
          try {
            const res = { formData: {} };
            let flag = true;
            const formData = await form.validateFields();
            if (formData) {
              res.formData = formData;
            } else {
              flag = false;
            }
            // 可编辑表格数据
            for (const tableName in editTableRefList) {
              const tableRef = editTableRefList[tableName];
              if (tableRef.current) {
                const data = await tableRef.current.validateFields();
                // 返回错误码
                if (data.errorCode) {
                  return data;
                } else if (data) {
                  res[tableName] = data;
                } else {
                  flag = false;
                }
              } else {
                flag = false;
              }
            }
            return flag ? res : null;
          } catch (error) {
            return null;
          }
        },
      };
    });

    // 监听表单数据
    const handleValuesChange = (changeValue: object, allValues: object): void => {
      onChange?.(changeValue, allValues);
      // 如果表单值发生变化时，除了获取changeValue, allValues外，还需要动态变更表单项的值时，可使用onLoad，在页面按需单独处理
      const obj = { changeValue, allValues, form };
      onLoad?.(obj);
    };

    return (
      <div id={id} className={`p-r p-t-s flex-1 flex-col flex-justify-between ${className}`}>
        <Skeleton loading={loading}>
          <Form
            ref={formRef}
            form={form}
            {...formLayout}
            colon={false}
            initialValues={initialData}
            labelWrap
            onValuesChange={_.debounce(handleValuesChange)}
          >
            {config.map((item, index) => renderFormItem(item, index))}
          </Form>
        </Skeleton>
        {!canEdit && showMaintenance ? <FormMaintenance data={initialData} /> : null}
      </div>
    );
  },
);

export default FormTemplate;
