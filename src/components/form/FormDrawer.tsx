/**
 * Created by ca<PERSON><PERSON> on 2025/6/6.
 * 表单抽屉（功能和formCard一样）：如果有自定义extra，优先自定义，再判断showAction展示默认提交/重置
 */
// 暂时没用上，不做处理
import { I18N_COMON_PAGENAME, OPERATE_TYPE } from '@/constants/publicConstant';
import useIntlCustom from '@/hooks/useIntlCustom';
import { CloseOutlined, SaveOutlined } from '@ant-design/icons';
import { Button, Drawer } from 'antd';
import _ from 'lodash';
import React from 'react';

interface IFormDrawerProps {
  children?: React.ReactNode;
  title?: React.ReactNode | string;
  extra?: React.ReactNode | string;
  type?: string;
  width?: string;
  open: boolean;
  submitTitle?: string;
  submitLoading?: boolean;
  intlPrefix?: string;
  onClose?: () => void;
  onSubmit?: () => void;
}
const FormDrawer: React.FC<IFormDrawerProps> = ({
  children,
  extra,
  title = '',
  type = '', // OPERATE_TYPE
  width = '',
  open = false,
  submitLoading = false,
  intlPrefix = '',
  onClose = () => {},
  onSubmit = () => {},
}) => {
  const { formatActionTitle, translate } = useIntlCustom();

  const getActionNode = () => {
    // 详情页
    if ([OPERATE_TYPE.detail, OPERATE_TYPE.userDetail, OPERATE_TYPE.fieldManage].includes(type)) {
      return (
        <Button type="primary" onClick={onClose}>
          {translate(I18N_COMON_PAGENAME.COMMON, 'close')}
        </Button>
      );
    }
    return (
      <>
        <Button icon={<CloseOutlined />} onClick={onClose} style={{ marginRight: '10px' }}>
          {translate(I18N_COMON_PAGENAME.COMMON, 'cancel')}
        </Button>
        <Button type="primary" icon={<SaveOutlined />} loading={submitLoading} onClick={_.debounce(onSubmit)}>
          {translate(I18N_COMON_PAGENAME.COMMON, 'submit')}
        </Button>
      </>
    );
  };

  return (
    <Drawer
      title={typeof title === 'string' ? formatActionTitle(type, intlPrefix, title) : title}
      placement="right"
      width={width || '70%'}
      open={open}
      maskClosable={false}
      extra={extra}
      destroyOnHidden
      footer={<div style={{ textAlign: 'right' }}>{getActionNode()}</div>}
      onClose={onClose}
    >
      {children || <div />}
    </Drawer>
  );
};
export default FormDrawer;
