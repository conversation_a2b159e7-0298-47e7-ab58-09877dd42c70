/**
 * 统一封装的搜索框
 * Created by ca<PERSON>un on 2025/6/6.
 */
import { COMPONENT_TYPE, LOCAL, NOT_BELONG_COMPONENT } from '@/constants/publicConstant';
import styleConstant, { themeColor } from '@/constants/styleConstant';
import useIntlCustom from '@/hooks/useIntlCustom'; 
import { PlusOutlined, RedoOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Cascader, Checkbox, DatePicker, Form, Input, Select, TreeSelect } from 'antd';
import _ from 'lodash';
import React, { ReactElement, useState } from 'react';
import GradientButton from '../GradientButton';
import { ISearchProps } from './ISearch';

// 默认宽度
const defaultWidth = 160;
// 样式
const mr2rem = { marginRight: '2rem' };
// 主题获取
const theme = localStorage.getItem(LOCAL.THEME) || themeColor.blue;

const Search: React.FC<ISearchProps> = ({
  children,
  searchTitle = '',
  resetTitle = '',
  createTitle = '',
  searchValue = { },
  resetValue = { },
  searchSource = [],
  intlPrefix = '',
  buttonDisabled = false,
  onSearch = () => {},
  onCreate = () => {},
  onChange,
  onRest,
}) => {
  // hook变量
  const { translate, getSelectOption, defaultInputPlaceholder, defaultSelectPlaceholder } = useIntlCustom();
  const [formValue, setFormValue] = useState(searchValue); 
  const [form] = Form.useForm();
  // 逻辑处理
  // 获取表单prop
  const getItemProp = (item) => {
    const itemProp = { rules: item.rules, name: item.value, label: undefined };
    if (item.label) {
      // 是否用自定义prefix
      const labelPrefix = item.prefix || intlPrefix;
      itemProp.label = labelPrefix ? translate(labelPrefix, item.label) : item.label;
    }
    return itemProp;
  };
  // 组件渲染逻辑处理
  const renderItem = (data) => {
    // 遍历剔除不属于组件的属性
    const itemProps = { allowClear: true, style: {} };
    // 获取不属于组件的属性的值
    const notBelongArray = Object.values(NOT_BELONG_COMPONENT);
    for (const p in data) {
      if (!notBelongArray.includes(p)) {
        itemProps[p] = data[p];
      }
    }
    const { type, width } = data;
    let item: ReactElement | null = null;
    switch (type) {
      case COMPONENT_TYPE.INPUT:
        itemProps.style = { width: width || defaultWidth };
        item = <Input placeholder={defaultInputPlaceholder} {...itemProps} />;
        return item;
      case COMPONENT_TYPE.CHECK_BOX:
        item = <Checkbox.Group options={data.data} />;
        return item;
      case COMPONENT_TYPE.SELECT: {
        itemProps.style = { width: width || defaultWidth };
        // 可以使用自定义模块
        const prefix = data.prefix || intlPrefix;
        // 是否需要国际化，默认需要
        let isInt = !(data.isint && data.isint === '0');
        // 把关联数据和全部选项过滤出来
        let selectSource = [];
        // 下拉框数据是否是查参数或字典，dictType对应store的dictMap对象的key
        if (_.isNil(data.dictType)) {
          selectSource =
            data.data && data.bind
              ? data.data.filter((t) => t.bind === formValue[data.bind] || t.key === '')
              : data.data;
        } else {
          // selectSource = dictState.dictMap[data.dictType];
          isInt = false; // 参数与字典不需要国际化
        }
        item = (
          <Select
            placeholder={defaultSelectPlaceholder}
            {...itemProps}
            options={isInt ? getSelectOption(selectSource, data.showKey, prefix) : selectSource}
          />
        );
        return item;
      }
      case COMPONENT_TYPE.TREE_SELECT:
        itemProps.style = { width: width || defaultWidth };
        item = <TreeSelect {...itemProps} treeData={data.data} />;
        return item;
      case COMPONENT_TYPE.CASCADER:
        itemProps.style = { width: width || defaultWidth };
        item = <Cascader placeholder={defaultSelectPlaceholder} {...itemProps} options={data.data} />;
        return item;
      case COMPONENT_TYPE.DATE_PICKER:
        itemProps.style = { width: width || defaultWidth };
        item = <DatePicker {...itemProps} />;
        return item;
      case COMPONENT_TYPE.RANGE_PICKER:
        itemProps.style = { width: width || 240 };
        item = <DatePicker.RangePicker {...itemProps} />;
        return item;
      default:
        return <Input {...itemProps} />;
    }
  };
  // 事件处理
  const handleValuesChange = (changedValues: object, allValues: object): void => {
    // 传入onChange事件则自行处理
    if (onChange) {
      onChange(changedValues, allValues);
    }
    // 获取联动的字段
    let key = Object.keys(changedValues)[0];
    let ref: string | any = '';
    searchSource.forEach((item) => {
      if (item.value === key && item.ref) {
        ref = item.ref;
      }
    });
    // 如果有联动字段，对应字段置空
    if (ref) {
      // 更新数据
      setFormValue({ ...allValues, [ref]: null });
      // 刷新页面
      form.setFieldsValue({ [ref]: null });
    } else {
      setFormValue(allValues);
    }
  };
  // 重置
  const handleClear = () => {
    form.setFieldsValue(resetValue); // 通过设置初始值达到重置效果
    if (onRest) {
      onRest(searchValue);
    }
    handleSearch();
  };
  // 搜索逻辑处理
  const handleSearch = () => {
    form
      .validateFields()
      .then((values) => {
        onSearch(values);
      })
      .catch((errors) => {
        // 表单验证失败，处理错误
        if (errors.errorFields) {
          // 显示错误提示
          form.setFields(errors.errorFields);
        }
      });
  };

  return (
    <div className="search-box">
      <Form
        layout="inline"
        colon={false}
        form={form}
        initialValues={searchValue}
        onValuesChange={_.debounce(handleValuesChange)}
      >
        {searchSource.map((item) => (
          <Form.Item key={item.value} {...getItemProp(item)} style={{ ...styleConstant.mtbs, ...mr2rem }}>
            {renderItem(item)}
          </Form.Item>
        ))}
        <Button
          type="primary"
          icon={<SearchOutlined />}
          disabled={buttonDisabled}
          style={styleConstant.mtbs}
          onClick={_.debounce(() => handleSearch())}
        >
          {searchTitle}
        </Button>
        <Button
          icon={<RedoOutlined />}
          disabled={buttonDisabled}
          style={{ ...styleConstant.mtbs, ...styleConstant.mls, ...mr2rem }}
          onClick={_.debounce(handleClear)}
        >
          {resetTitle}
        </Button>
        {createTitle && (
          <GradientButton
            color={theme}
            type="primary"
            size="middle"
            icon={<PlusOutlined />}
            disabled={buttonDisabled}
            style={{ ...styleConstant.mtbs, ...mr2rem }}
            onClick={onCreate}
          >
            {createTitle}
          </GradientButton>
        )}
        {children && <div style={styleConstant.mtbs}>{children}</div>}
      </Form>
    </div>
  );
};
export default Search;
