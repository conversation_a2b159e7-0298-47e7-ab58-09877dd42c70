 import { Modal } from 'antd';
import { FC, memo } from 'react';
import config from './config';

interface IModalComType {
  type: string;
  open: boolean;
  content: any;
  onClose: () => void;
  onSubmit: () => void;
}

const ModalCom: FC<IModalComType> = ({ type, open, content, onClose, onSubmit }) => {


  const handleOk = () => {
    onSubmit?.();
  };

  return (
    <Modal
      width="55%"
      open={open}
      centered
      maskClosable={false}
      {...config[type]}
      title={config[type].title}
      onCancel={onClose}
      onOk={handleOk}
    >
      {content()}
    </Modal>
  );
};

export default memo(ModalCom);
