import LayoutTemplate  from './LayoutTemplate';
import { DEFAULT_PAGINATION, OPERATE_TYPE } from '@/constants/publicConstant';

import { isGetTableData } from '@/utils/comUtil';
import { Spin } from 'antd';
import React, { Suspense, useEffect, useRef, useState } from 'react';
import usePageForm from './hooks/usePageForm';
import usePageFormAction from './hooks/usePageFormAction';
import usePageSearch from './hooks/usePageSearch';
import usePageTable from './hooks/usePageTable';
import usePageUrl from './hooks/usePageUrl';
import { IPageTemplateProps } from './types/IPageTemplate';

const PageTemplate: React.FC<IPageTemplateProps> = ({
  searchConfig,
  tableConfig,
  formConfig,
  formActionConfig,
  urlObj,
  isShowRouterBar,
  cardTitle,
  intlPrefix,
  dictEnum,
  cardExtra,
  formExtra,
  onAction = () => {},
}) => {
  // hooks变量
  // 操作类型
  const [type, setType] = useState<string>(OPERATE_TYPE.list);
  // 操作行数据
  const [rowData, setRowData] = useState<object>({});
  // 分页配置
  const [pagination, setPagination] = useState({ ...DEFAULT_PAGINATION });
  // 是否打开弹层
  const [drawerOpen, setDrawerOpen] = useState<boolean>(false);
  const formRef = useRef<any>(null);
  // 页面需要查询的字典或参数字段

  // 请求接口hooks
  const { tableData, tableLoading, paginationConfig, getTableData, getRequestFunc } = usePageUrl({
    searchConfig,
    tableConfig,
    urlObj,
  });
  // search组件hooks
  const { searchValue, renderSearch } = usePageSearch({
    searchConfig,
    intlPrefix,
    pagination,
    getTableData,
  });

  // table组件hooks
  const { renderTable, handleAction } = usePageTable({
    tableConfig,
    urlObj,
    pagination,
    paginationConfig,
    tableData,
    tableLoading,
    intlPrefix,
    setType,
    setRowData,
    setPagination,
    getRequestFunc,
    getTableData,
    onAction,
    setDrawerOpen,
  });

  // form组件hooks
  const { renderForm } = usePageForm({ formRef, formConfig, type });

  // 返回事件
  const handleCardBack = (): void => {
    // setType('');
    setDrawerOpen(false);
    onAction(OPERATE_TYPE.list, {});
  };

  // formAction组件hooks
  const { renderFormAction, handleDrawerSubmit } = usePageFormAction({
    formRef,
    type,
    rowData,
    searchValue,
    pagination,
    urlObj,
    formActionConfig,
    cardExtra,
    getTableData,
    handleAction,
    handleCardBack,
  });

  // 副作用
  useEffect(() => {
    // 查询条件有必输，首次则不掉用接口/或者是参数首次不调用,common组件会调用分页查询数据
    if (isGetTableData(searchConfig)) return;
    getTableData();
  }, []);

  return (
    <Suspense fallback={<Spin size="large" />}>
      <LayoutTemplate
        searchChildren={renderSearch()}
        tableChildren={renderTable()}
        formChildren={renderForm()}
        formExtra={formExtra}
        type={type}
        intlPrefix={intlPrefix}
        cardTitle={cardTitle}
        isShowRouterBar={isShowRouterBar}
        cardExtra={renderFormAction()}
        handleClose={handleCardBack}
        handleSubmit={handleDrawerSubmit}
        drawerOpen={drawerOpen}
      />
    </Suspense>
  );
};

export default PageTemplate;
