import * as $ from "react";
import Ze, { memo as Rn, createContext as Mn, useContext as $n, useEffect as Qe, useState as Ve, use<PERSON><PERSON>back as ut, useMemo as Wt, forwardRef as Pn, useRef as Kt, useImperativeHandle as Nn } from "react";
import { Button as Ee, Typography as Dn, Card as An, Modal as jn, Tooltip as Je, Popconfirm as In, Space as kn, notification as Ln, Descriptions as Hn, ConfigProvider as Fn, Form as Xe, Input as Gt, DatePicker as qt, Cascader as Vn, TreeSelect as zn, Select as Yn, Checkbox as Bn, Table as Un, Upload as ft, message as Ge } from "antd";
import he, { isString as Wn, cloneDeep as Kn } from "lodash";
import { ProCard as Gn, EditableProTable as qn } from "@ant-design/pro-components";
var Jn = typeof globalThis < "u" ? globalThis : typeof window < "u" ? window : typeof global < "u" ? global : typeof self < "u" ? self : {};
function yr(e) {
  return e && e.__esModule && Object.prototype.hasOwnProperty.call(e, "default") ? e.default : e;
}
var pt = { exports: {} }, Ie = {};
/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Jt;
function Xn() {
  if (Jt) return Ie;
  Jt = 1;
  var e = Ze, t = Symbol.for("react.element"), r = Symbol.for("react.fragment"), n = Object.prototype.hasOwnProperty, o = e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner, i = { key: !0, ref: !0, __self: !0, __source: !0 };
  function s(f, c, l) {
    var g, d = {}, I = null, k = null;
    l !== void 0 && (I = "" + l), c.key !== void 0 && (I = "" + c.key), c.ref !== void 0 && (k = c.ref);
    for (g in c) n.call(c, g) && !i.hasOwnProperty(g) && (d[g] = c[g]);
    if (f && f.defaultProps) for (g in c = f.defaultProps, c) d[g] === void 0 && (d[g] = c[g]);
    return { $$typeof: t, type: f, key: I, ref: k, props: d, _owner: o.current };
  }
  return Ie.Fragment = r, Ie.jsx = s, Ie.jsxs = s, Ie;
}
var ke = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Xt;
function Zn() {
  return Xt || (Xt = 1, process.env.NODE_ENV !== "production" && function() {
    var e = Ze, t = Symbol.for("react.element"), r = Symbol.for("react.portal"), n = Symbol.for("react.fragment"), o = Symbol.for("react.strict_mode"), i = Symbol.for("react.profiler"), s = Symbol.for("react.provider"), f = Symbol.for("react.context"), c = Symbol.for("react.forward_ref"), l = Symbol.for("react.suspense"), g = Symbol.for("react.suspense_list"), d = Symbol.for("react.memo"), I = Symbol.for("react.lazy"), k = Symbol.for("react.offscreen"), T = Symbol.iterator, x = "@@iterator";
    function N(a) {
      if (a === null || typeof a != "object")
        return null;
      var h = T && a[T] || a[x];
      return typeof h == "function" ? h : null;
    }
    var E = e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
    function C(a) {
      {
        for (var h = arguments.length, O = new Array(h > 1 ? h - 1 : 0), j = 1; j < h; j++)
          O[j - 1] = arguments[j];
        b("error", a, O);
      }
    }
    function b(a, h, O) {
      {
        var j = E.ReactDebugCurrentFrame, W = j.getStackAddendum();
        W !== "" && (h += "%s", O = O.concat([W]));
        var X = O.map(function(z) {
          return String(z);
        });
        X.unshift("Warning: " + h), Function.prototype.apply.call(console[a], console, X);
      }
    }
    var M = !1, u = !1, H = !1, V = !1, J = !1, R;
    R = Symbol.for("react.module.reference");
    function P(a) {
      return !!(typeof a == "string" || typeof a == "function" || a === n || a === i || J || a === o || a === l || a === g || V || a === k || M || u || H || typeof a == "object" && a !== null && (a.$$typeof === I || a.$$typeof === d || a.$$typeof === s || a.$$typeof === f || a.$$typeof === c || // This needs to include all possible module reference object
      // types supported by any Flight configuration anywhere since
      // we don't know which Flight build this will end up being used
      // with.
      a.$$typeof === R || a.getModuleId !== void 0));
    }
    function L(a, h, O) {
      var j = a.displayName;
      if (j)
        return j;
      var W = h.displayName || h.name || "";
      return W !== "" ? O + "(" + W + ")" : O;
    }
    function B(a) {
      return a.displayName || "Context";
    }
    function Y(a) {
      if (a == null)
        return null;
      if (typeof a.tag == "number" && C("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), typeof a == "function")
        return a.displayName || a.name || null;
      if (typeof a == "string")
        return a;
      switch (a) {
        case n:
          return "Fragment";
        case r:
          return "Portal";
        case i:
          return "Profiler";
        case o:
          return "StrictMode";
        case l:
          return "Suspense";
        case g:
          return "SuspenseList";
      }
      if (typeof a == "object")
        switch (a.$$typeof) {
          case f:
            var h = a;
            return B(h) + ".Consumer";
          case s:
            var O = a;
            return B(O._context) + ".Provider";
          case c:
            return L(a, a.render, "ForwardRef");
          case d:
            var j = a.displayName || null;
            return j !== null ? j : Y(a.type) || "Memo";
          case I: {
            var W = a, X = W._payload, z = W._init;
            try {
              return Y(z(X));
            } catch {
              return null;
            }
          }
        }
      return null;
    }
    var m = Object.assign, v = 0, p, w, y, A, D, U, Z;
    function Q() {
    }
    Q.__reactDisabledLog = !0;
    function ae() {
      {
        if (v === 0) {
          p = console.log, w = console.info, y = console.warn, A = console.error, D = console.group, U = console.groupCollapsed, Z = console.groupEnd;
          var a = {
            configurable: !0,
            enumerable: !0,
            value: Q,
            writable: !0
          };
          Object.defineProperties(console, {
            info: a,
            log: a,
            warn: a,
            error: a,
            group: a,
            groupCollapsed: a,
            groupEnd: a
          });
        }
        v++;
      }
    }
    function se() {
      {
        if (v--, v === 0) {
          var a = {
            configurable: !0,
            enumerable: !0,
            writable: !0
          };
          Object.defineProperties(console, {
            log: m({}, a, {
              value: p
            }),
            info: m({}, a, {
              value: w
            }),
            warn: m({}, a, {
              value: y
            }),
            error: m({}, a, {
              value: A
            }),
            group: m({}, a, {
              value: D
            }),
            groupCollapsed: m({}, a, {
              value: U
            }),
            groupEnd: m({}, a, {
              value: Z
            })
          });
        }
        v < 0 && C("disabledDepth fell below zero. This is a bug in React. Please file an issue.");
      }
    }
    var me = E.ReactCurrentDispatcher, fe;
    function ve(a, h, O) {
      {
        if (fe === void 0)
          try {
            throw Error();
          } catch (W) {
            var j = W.stack.trim().match(/\n( *(at )?)/);
            fe = j && j[1] || "";
          }
        return `
` + fe + a;
      }
    }
    var de = !1, re;
    {
      var pe = typeof WeakMap == "function" ? WeakMap : Map;
      re = new pe();
    }
    function S(a, h) {
      if (!a || de)
        return "";
      {
        var O = re.get(a);
        if (O !== void 0)
          return O;
      }
      var j;
      de = !0;
      var W = Error.prepareStackTrace;
      Error.prepareStackTrace = void 0;
      var X;
      X = me.current, me.current = null, ae();
      try {
        if (h) {
          var z = function() {
            throw Error();
          };
          if (Object.defineProperty(z.prototype, "props", {
            set: function() {
              throw Error();
            }
          }), typeof Reflect == "object" && Reflect.construct) {
            try {
              Reflect.construct(z, []);
            } catch (ue) {
              j = ue;
            }
            Reflect.construct(a, [], z);
          } else {
            try {
              z.call();
            } catch (ue) {
              j = ue;
            }
            a.call(z.prototype);
          }
        } else {
          try {
            throw Error();
          } catch (ue) {
            j = ue;
          }
          a();
        }
      } catch (ue) {
        if (ue && j && typeof ue.stack == "string") {
          for (var F = ue.stack.split(`
`), ce = j.stack.split(`
`), ne = F.length - 1, oe = ce.length - 1; ne >= 1 && oe >= 0 && F[ne] !== ce[oe]; )
            oe--;
          for (; ne >= 1 && oe >= 0; ne--, oe--)
            if (F[ne] !== ce[oe]) {
              if (ne !== 1 || oe !== 1)
                do
                  if (ne--, oe--, oe < 0 || F[ne] !== ce[oe]) {
                    var ge = `
` + F[ne].replace(" at new ", " at ");
                    return a.displayName && ge.includes("<anonymous>") && (ge = ge.replace("<anonymous>", a.displayName)), typeof a == "function" && re.set(a, ge), ge;
                  }
                while (ne >= 1 && oe >= 0);
              break;
            }
        }
      } finally {
        de = !1, me.current = X, se(), Error.prepareStackTrace = W;
      }
      var $e = a ? a.displayName || a.name : "", Te = $e ? ve($e) : "";
      return typeof a == "function" && re.set(a, Te), Te;
    }
    function ye(a, h, O) {
      return S(a, !1);
    }
    function Re(a) {
      var h = a.prototype;
      return !!(h && h.isReactComponent);
    }
    function Oe(a, h, O) {
      if (a == null)
        return "";
      if (typeof a == "function")
        return S(a, Re(a));
      if (typeof a == "string")
        return ve(a);
      switch (a) {
        case l:
          return ve("Suspense");
        case g:
          return ve("SuspenseList");
      }
      if (typeof a == "object")
        switch (a.$$typeof) {
          case c:
            return ye(a.render);
          case d:
            return Oe(a.type, h, O);
          case I: {
            var j = a, W = j._payload, X = j._init;
            try {
              return Oe(X(W), h, O);
            } catch {
            }
          }
        }
      return "";
    }
    var je = Object.prototype.hasOwnProperty, Nt = {}, Dt = E.ReactDebugCurrentFrame;
    function Ke(a) {
      if (a) {
        var h = a._owner, O = Oe(a.type, a._source, h ? h.type : null);
        Dt.setExtraStackFrame(O);
      } else
        Dt.setExtraStackFrame(null);
    }
    function on(a, h, O, j, W) {
      {
        var X = Function.call.bind(je);
        for (var z in a)
          if (X(a, z)) {
            var F = void 0;
            try {
              if (typeof a[z] != "function") {
                var ce = Error((j || "React class") + ": " + O + " type `" + z + "` is invalid; it must be a function, usually from the `prop-types` package, but received `" + typeof a[z] + "`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");
                throw ce.name = "Invariant Violation", ce;
              }
              F = a[z](h, z, j, O, null, "SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED");
            } catch (ne) {
              F = ne;
            }
            F && !(F instanceof Error) && (Ke(W), C("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).", j || "React class", O, z, typeof F), Ke(null)), F instanceof Error && !(F.message in Nt) && (Nt[F.message] = !0, Ke(W), C("Failed %s type: %s", O, F.message), Ke(null));
          }
      }
    }
    var sn = Array.isArray;
    function ot(a) {
      return sn(a);
    }
    function cn(a) {
      {
        var h = typeof Symbol == "function" && Symbol.toStringTag, O = h && a[Symbol.toStringTag] || a.constructor.name || "Object";
        return O;
      }
    }
    function ln(a) {
      try {
        return At(a), !1;
      } catch {
        return !0;
      }
    }
    function At(a) {
      return "" + a;
    }
    function jt(a) {
      if (ln(a))
        return C("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.", cn(a)), At(a);
    }
    var It = E.ReactCurrentOwner, un = {
      key: !0,
      ref: !0,
      __self: !0,
      __source: !0
    }, kt, Lt;
    function fn(a) {
      if (je.call(a, "ref")) {
        var h = Object.getOwnPropertyDescriptor(a, "ref").get;
        if (h && h.isReactWarning)
          return !1;
      }
      return a.ref !== void 0;
    }
    function dn(a) {
      if (je.call(a, "key")) {
        var h = Object.getOwnPropertyDescriptor(a, "key").get;
        if (h && h.isReactWarning)
          return !1;
      }
      return a.key !== void 0;
    }
    function hn(a, h) {
      typeof a.ref == "string" && It.current;
    }
    function mn(a, h) {
      {
        var O = function() {
          kt || (kt = !0, C("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)", h));
        };
        O.isReactWarning = !0, Object.defineProperty(a, "key", {
          get: O,
          configurable: !0
        });
      }
    }
    function vn(a, h) {
      {
        var O = function() {
          Lt || (Lt = !0, C("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)", h));
        };
        O.isReactWarning = !0, Object.defineProperty(a, "ref", {
          get: O,
          configurable: !0
        });
      }
    }
    var pn = function(a, h, O, j, W, X, z) {
      var F = {
        // This tag allows us to uniquely identify this as a React Element
        $$typeof: t,
        // Built-in properties that belong on the element
        type: a,
        key: h,
        ref: O,
        props: z,
        // Record the component responsible for creating this element.
        _owner: X
      };
      return F._store = {}, Object.defineProperty(F._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: !1
      }), Object.defineProperty(F, "_self", {
        configurable: !1,
        enumerable: !1,
        writable: !1,
        value: j
      }), Object.defineProperty(F, "_source", {
        configurable: !1,
        enumerable: !1,
        writable: !1,
        value: W
      }), Object.freeze && (Object.freeze(F.props), Object.freeze(F)), F;
    };
    function gn(a, h, O, j, W) {
      {
        var X, z = {}, F = null, ce = null;
        O !== void 0 && (jt(O), F = "" + O), dn(h) && (jt(h.key), F = "" + h.key), fn(h) && (ce = h.ref, hn(h, W));
        for (X in h)
          je.call(h, X) && !un.hasOwnProperty(X) && (z[X] = h[X]);
        if (a && a.defaultProps) {
          var ne = a.defaultProps;
          for (X in ne)
            z[X] === void 0 && (z[X] = ne[X]);
        }
        if (F || ce) {
          var oe = typeof a == "function" ? a.displayName || a.name || "Unknown" : a;
          F && mn(z, oe), ce && vn(z, oe);
        }
        return pn(a, F, ce, W, j, It.current, z);
      }
    }
    var it = E.ReactCurrentOwner, Ht = E.ReactDebugCurrentFrame;
    function Me(a) {
      if (a) {
        var h = a._owner, O = Oe(a.type, a._source, h ? h.type : null);
        Ht.setExtraStackFrame(O);
      } else
        Ht.setExtraStackFrame(null);
    }
    var st;
    st = !1;
    function ct(a) {
      return typeof a == "object" && a !== null && a.$$typeof === t;
    }
    function Ft() {
      {
        if (it.current) {
          var a = Y(it.current.type);
          if (a)
            return `

Check the render method of \`` + a + "`.";
        }
        return "";
      }
    }
    function yn(a) {
      return "";
    }
    var Vt = {};
    function bn(a) {
      {
        var h = Ft();
        if (!h) {
          var O = typeof a == "string" ? a : a.displayName || a.name;
          O && (h = `

Check the top-level render call using <` + O + ">.");
        }
        return h;
      }
    }
    function zt(a, h) {
      {
        if (!a._store || a._store.validated || a.key != null)
          return;
        a._store.validated = !0;
        var O = bn(h);
        if (Vt[O])
          return;
        Vt[O] = !0;
        var j = "";
        a && a._owner && a._owner !== it.current && (j = " It was passed a child from " + Y(a._owner.type) + "."), Me(a), C('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.', O, j), Me(null);
      }
    }
    function Yt(a, h) {
      {
        if (typeof a != "object")
          return;
        if (ot(a))
          for (var O = 0; O < a.length; O++) {
            var j = a[O];
            ct(j) && zt(j, h);
          }
        else if (ct(a))
          a._store && (a._store.validated = !0);
        else if (a) {
          var W = N(a);
          if (typeof W == "function" && W !== a.entries)
            for (var X = W.call(a), z; !(z = X.next()).done; )
              ct(z.value) && zt(z.value, h);
        }
      }
    }
    function En(a) {
      {
        var h = a.type;
        if (h == null || typeof h == "string")
          return;
        var O;
        if (typeof h == "function")
          O = h.propTypes;
        else if (typeof h == "object" && (h.$$typeof === c || // Note: Memo only checks outer props here.
        // Inner props are checked in the reconciler.
        h.$$typeof === d))
          O = h.propTypes;
        else
          return;
        if (O) {
          var j = Y(h);
          on(O, a.props, "prop", j, a);
        } else if (h.PropTypes !== void 0 && !st) {
          st = !0;
          var W = Y(h);
          C("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?", W || "Unknown");
        }
        typeof h.getDefaultProps == "function" && !h.getDefaultProps.isReactClassApproved && C("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.");
      }
    }
    function Cn(a) {
      {
        for (var h = Object.keys(a.props), O = 0; O < h.length; O++) {
          var j = h[O];
          if (j !== "children" && j !== "key") {
            Me(a), C("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.", j), Me(null);
            break;
          }
        }
        a.ref !== null && (Me(a), C("Invalid attribute `ref` supplied to `React.Fragment`."), Me(null));
      }
    }
    var Bt = {};
    function Ut(a, h, O, j, W, X) {
      {
        var z = P(a);
        if (!z) {
          var F = "";
          (a === void 0 || typeof a == "object" && a !== null && Object.keys(a).length === 0) && (F += " You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");
          var ce = yn();
          ce ? F += ce : F += Ft();
          var ne;
          a === null ? ne = "null" : ot(a) ? ne = "array" : a !== void 0 && a.$$typeof === t ? (ne = "<" + (Y(a.type) || "Unknown") + " />", F = " Did you accidentally export a JSX literal instead of a component?") : ne = typeof a, C("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s", ne, F);
        }
        var oe = gn(a, h, O, W, X);
        if (oe == null)
          return oe;
        if (z) {
          var ge = h.children;
          if (ge !== void 0)
            if (j)
              if (ot(ge)) {
                for (var $e = 0; $e < ge.length; $e++)
                  Yt(ge[$e], a);
                Object.freeze && Object.freeze(ge);
              } else
                C("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");
            else
              Yt(ge, a);
        }
        if (je.call(h, "key")) {
          var Te = Y(a), ue = Object.keys(h).filter(function(Sn) {
            return Sn !== "key";
          }), lt = ue.length > 0 ? "{key: someKey, " + ue.join(": ..., ") + ": ...}" : "{key: someKey}";
          if (!Bt[Te + lt]) {
            var wn = ue.length > 0 ? "{" + ue.join(": ..., ") + ": ...}" : "{}";
            C(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`, lt, Te, wn, Te), Bt[Te + lt] = !0;
          }
        }
        return a === n ? Cn(oe) : En(oe), oe;
      }
    }
    function _n(a, h, O) {
      return Ut(a, h, O, !0);
    }
    function On(a, h, O) {
      return Ut(a, h, O, !1);
    }
    var Tn = On, xn = _n;
    ke.Fragment = n, ke.jsx = Tn, ke.jsxs = xn;
  }()), ke;
}
process.env.NODE_ENV === "production" ? pt.exports = Xn() : pt.exports = Zn();
var _ = pt.exports;
const fi = ({
  text: e = "按钮",
  type: t = "default",
  onClick: r,
  size: n = "middle"
}) => {
  const o = Wn(e) ? e : "按钮";
  return /* @__PURE__ */ _.jsx(
    Ee,
    {
      type: t,
      size: n,
      onClick: r,
      style: { margin: "0 4px" },
      children: o
    }
  );
}, { Title: Qn, Paragraph: Zt } = Dn, di = ({
  title: e,
  content: t,
  usePro: r = !1,
  style: n
}) => {
  const o = Kn(t), i = {
    margin: "16px",
    padding: "16px",
    ...n
  };
  return r ? /* @__PURE__ */ _.jsx(
    Gn,
    {
      title: e,
      style: i,
      bordered: !0,
      children: /* @__PURE__ */ _.jsx(Zt, { children: o })
    }
  ) : /* @__PURE__ */ _.jsx(
    An,
    {
      title: /* @__PURE__ */ _.jsx(Qn, { level: 5, children: e }),
      style: i,
      bordered: !0,
      children: /* @__PURE__ */ _.jsx(Zt, { children: o })
    }
  );
}, Qt = {
  MESSAGE_CALL_MODAL: {
    title: "messageCallTitle"
  },
  PHONE_CALL_MODAL: {
    title: "phoneCallTitle"
  },
  CALL_PERRSON_MODAL: {
    title: "callPersonTitle"
  },
  AI_HELP_MODAL: {
    title: "aiHelpTitle",
    okText: "aiHelpOkText"
  },
  CALL_BLOCKCODE_MODAL: {
    title: "blockCodeTitle",
    width: 500
  },
  CALL_DIS_CUSTOMIZE_MODAL: {
    title: "dispatchTitle",
    width: 500
  },
  CALL_TRANS_CUSTOMIZE_MODAL: {
    title: "transferTitle",
    width: 500
  }
}, ea = ({ type: e, open: t, content: r, onClose: n, onSubmit: o }) => {
  const i = () => {
    o == null || o();
  };
  return /* @__PURE__ */ _.jsx(
    jn,
    {
      width: "55%",
      open: t,
      centered: !0,
      maskClosable: !1,
      ...Qt[e],
      title: Qt[e].title,
      onCancel: n,
      onOk: i,
      children: r()
    }
  );
}, hi = Rn(ea), ta = "YYYY-MM-DD", ra = "YYYY-MM-DD HH:mm:ss", q = {
  create: "create",
  detail: "detail",
  copy: "copy",
  edit: "edit",
  delete: "delete",
  list: "list",
  cancel: "cancel",
  save: "save",
  up: "up",
  down: "down",
  submit: "submit",
  flowNode: "flowNode",
  dispatch: "dispatch",
  dowload: "dowload",
  aiHelp: "aiHelp",
  menuAuth: "menuAuth",
  userDetail: "userDetail",
  fieldManage: "fieldManage"
}, rt = {
  THEME: "theme"
}, na = {
  EN: { locale: "en" }
}, xe = {
  // antd 组件
  INPUT: "Input",
  CHECK_BOX: "Checkbox",
  SELECT: "Select",
  TREE_SELECT: "TreeSelect",
  CASCADER: "Cascader",
  DATE_PICKER: "DatePicker",
  RANGE_PICKER: "RangePicker"
}, Ne = {
  Amount: "Amount",
  Dictionary: "Dictionary",
  MockDictionary: "MockDictionary",
  Ellipsis: "ellipsis",
  DateTime: "DateTime",
  Date: "Date"
}, dt = {
  WARNING: "warning"
}, aa = {
  // 数字
  SELECT: "select"
}, oa = {
  CHECK_MSG: "checkMsg",
  EDITING: "formEditing"
}, Ce = {
  COMMON: "common"
}, ia = {
  TYPE: "type",
  VALUE: "value",
  LABEL: "label",
  RULES: "rules",
  DATA: "data",
  SHOWKEY: "showKey",
  REF: "ref",
  BIND: "bind",
  PREFIX: "prefix",
  HIDE: "hide",
  DEPENDENCIES: "dependencies",
  DICTtYPE: "dictType",
  FORMLAYOUTTYPE: "formlayoutType",
  COL_SPAN: "colSpan",
  PARSER: "parser",
  SHOULD_UPDATE: "shouldUpdate",
  SET_VISIBLE_FUNC: "setVisibleFunc"
};
var br = /* @__PURE__ */ Mn({});
function ee() {
  return ee = Object.assign ? Object.assign.bind() : function(e) {
    for (var t = 1; t < arguments.length; t++) {
      var r = arguments[t];
      for (var n in r) ({}).hasOwnProperty.call(r, n) && (e[n] = r[n]);
    }
    return e;
  }, ee.apply(null, arguments);
}
function sa(e) {
  if (Array.isArray(e)) return e;
}
function ca(e, t) {
  var r = e == null ? null : typeof Symbol < "u" && e[Symbol.iterator] || e["@@iterator"];
  if (r != null) {
    var n, o, i, s, f = [], c = !0, l = !1;
    try {
      if (i = (r = r.call(e)).next, t !== 0) for (; !(c = (n = i.call(r)).done) && (f.push(n.value), f.length !== t); c = !0) ;
    } catch (g) {
      l = !0, o = g;
    } finally {
      try {
        if (!c && r.return != null && (s = r.return(), Object(s) !== s)) return;
      } finally {
        if (l) throw o;
      }
    }
    return f;
  }
}
function er(e, t) {
  (t == null || t > e.length) && (t = e.length);
  for (var r = 0, n = Array(t); r < t; r++) n[r] = e[r];
  return n;
}
function la(e, t) {
  if (e) {
    if (typeof e == "string") return er(e, t);
    var r = {}.toString.call(e).slice(8, -1);
    return r === "Object" && e.constructor && (r = e.constructor.name), r === "Map" || r === "Set" ? Array.from(e) : r === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r) ? er(e, t) : void 0;
  }
}
function ua() {
  throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`);
}
function Er(e, t) {
  return sa(e) || ca(e, t) || la(e, t) || ua();
}
function Se(e) {
  "@babel/helpers - typeof";
  return Se = typeof Symbol == "function" && typeof Symbol.iterator == "symbol" ? function(t) {
    return typeof t;
  } : function(t) {
    return t && typeof Symbol == "function" && t.constructor === Symbol && t !== Symbol.prototype ? "symbol" : typeof t;
  }, Se(e);
}
function fa(e, t) {
  if (Se(e) != "object" || !e) return e;
  var r = e[Symbol.toPrimitive];
  if (r !== void 0) {
    var n = r.call(e, t);
    if (Se(n) != "object") return n;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return (t === "string" ? String : Number)(e);
}
function da(e) {
  var t = fa(e, "string");
  return Se(t) == "symbol" ? t : t + "";
}
function le(e, t, r) {
  return (t = da(t)) in e ? Object.defineProperty(e, t, {
    value: r,
    enumerable: !0,
    configurable: !0,
    writable: !0
  }) : e[t] = r, e;
}
function ha(e, t) {
  if (e == null) return {};
  var r = {};
  for (var n in e) if ({}.hasOwnProperty.call(e, n)) {
    if (t.indexOf(n) !== -1) continue;
    r[n] = e[n];
  }
  return r;
}
function Cr(e, t) {
  if (e == null) return {};
  var r, n, o = ha(e, t);
  if (Object.getOwnPropertySymbols) {
    var i = Object.getOwnPropertySymbols(e);
    for (n = 0; n < i.length; n++) r = i[n], t.indexOf(r) === -1 && {}.propertyIsEnumerable.call(e, r) && (o[r] = e[r]);
  }
  return o;
}
var _r = { exports: {} };
/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/
(function(e) {
  (function() {
    var t = {}.hasOwnProperty;
    function r() {
      for (var i = "", s = 0; s < arguments.length; s++) {
        var f = arguments[s];
        f && (i = o(i, n(f)));
      }
      return i;
    }
    function n(i) {
      if (typeof i == "string" || typeof i == "number")
        return i;
      if (typeof i != "object")
        return "";
      if (Array.isArray(i))
        return r.apply(null, i);
      if (i.toString !== Object.prototype.toString && !i.toString.toString().includes("[native code]"))
        return i.toString();
      var s = "";
      for (var f in i)
        t.call(i, f) && i[f] && (s = o(s, f));
      return s;
    }
    function o(i, s) {
      return s ? i ? i + " " + s : i + s : i;
    }
    e.exports ? (r.default = r, e.exports = r) : window.classNames = r;
  })();
})(_r);
var ma = _r.exports;
const va = /* @__PURE__ */ yr(ma), ie = Math.round;
function ht(e, t) {
  const r = e.replace(/^[^(]*\((.*)/, "$1").replace(/\).*/, "").match(/\d*\.?\d+%?/g) || [], n = r.map((o) => parseFloat(o));
  for (let o = 0; o < 3; o += 1)
    n[o] = t(n[o] || 0, r[o] || "", o);
  return r[3] ? n[3] = r[3].includes("%") ? n[3] / 100 : n[3] : n[3] = 1, n;
}
const tr = (e, t, r) => r === 0 ? e : e / 100;
function Le(e, t) {
  const r = t || 255;
  return e > r ? r : e < 0 ? 0 : e;
}
class De {
  constructor(t) {
    le(this, "isValid", !0), le(this, "r", 0), le(this, "g", 0), le(this, "b", 0), le(this, "a", 1), le(this, "_h", void 0), le(this, "_s", void 0), le(this, "_l", void 0), le(this, "_v", void 0), le(this, "_max", void 0), le(this, "_min", void 0), le(this, "_brightness", void 0);
    function r(n) {
      return n[0] in t && n[1] in t && n[2] in t;
    }
    if (t) if (typeof t == "string") {
      let o = function(i) {
        return n.startsWith(i);
      };
      const n = t.trim();
      /^#?[A-F\d]{3,8}$/i.test(n) ? this.fromHexString(n) : o("rgb") ? this.fromRgbString(n) : o("hsl") ? this.fromHslString(n) : (o("hsv") || o("hsb")) && this.fromHsvString(n);
    } else if (t instanceof De)
      this.r = t.r, this.g = t.g, this.b = t.b, this.a = t.a, this._h = t._h, this._s = t._s, this._l = t._l, this._v = t._v;
    else if (r("rgb"))
      this.r = Le(t.r), this.g = Le(t.g), this.b = Le(t.b), this.a = typeof t.a == "number" ? Le(t.a, 1) : 1;
    else if (r("hsl"))
      this.fromHsl(t);
    else if (r("hsv"))
      this.fromHsv(t);
    else
      throw new Error("@ant-design/fast-color: unsupported input " + JSON.stringify(t));
  }
  // ======================= Setter =======================
  setR(t) {
    return this._sc("r", t);
  }
  setG(t) {
    return this._sc("g", t);
  }
  setB(t) {
    return this._sc("b", t);
  }
  setA(t) {
    return this._sc("a", t, 1);
  }
  setHue(t) {
    const r = this.toHsv();
    return r.h = t, this._c(r);
  }
  // ======================= Getter =======================
  /**
   * Returns the perceived luminance of a color, from 0-1.
   * @see http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef
   */
  getLuminance() {
    function t(i) {
      const s = i / 255;
      return s <= 0.03928 ? s / 12.92 : Math.pow((s + 0.055) / 1.055, 2.4);
    }
    const r = t(this.r), n = t(this.g), o = t(this.b);
    return 0.2126 * r + 0.7152 * n + 0.0722 * o;
  }
  getHue() {
    if (typeof this._h > "u") {
      const t = this.getMax() - this.getMin();
      t === 0 ? this._h = 0 : this._h = ie(60 * (this.r === this.getMax() ? (this.g - this.b) / t + (this.g < this.b ? 6 : 0) : this.g === this.getMax() ? (this.b - this.r) / t + 2 : (this.r - this.g) / t + 4));
    }
    return this._h;
  }
  getSaturation() {
    if (typeof this._s > "u") {
      const t = this.getMax() - this.getMin();
      t === 0 ? this._s = 0 : this._s = t / this.getMax();
    }
    return this._s;
  }
  getLightness() {
    return typeof this._l > "u" && (this._l = (this.getMax() + this.getMin()) / 510), this._l;
  }
  getValue() {
    return typeof this._v > "u" && (this._v = this.getMax() / 255), this._v;
  }
  /**
   * Returns the perceived brightness of the color, from 0-255.
   * Note: this is not the b of HSB
   * @see http://www.w3.org/TR/AERT#color-contrast
   */
  getBrightness() {
    return typeof this._brightness > "u" && (this._brightness = (this.r * 299 + this.g * 587 + this.b * 114) / 1e3), this._brightness;
  }
  // ======================== Func ========================
  darken(t = 10) {
    const r = this.getHue(), n = this.getSaturation();
    let o = this.getLightness() - t / 100;
    return o < 0 && (o = 0), this._c({
      h: r,
      s: n,
      l: o,
      a: this.a
    });
  }
  lighten(t = 10) {
    const r = this.getHue(), n = this.getSaturation();
    let o = this.getLightness() + t / 100;
    return o > 1 && (o = 1), this._c({
      h: r,
      s: n,
      l: o,
      a: this.a
    });
  }
  /**
   * Mix the current color a given amount with another color, from 0 to 100.
   * 0 means no mixing (return current color).
   */
  mix(t, r = 50) {
    const n = this._c(t), o = r / 100, i = (f) => (n[f] - this[f]) * o + this[f], s = {
      r: ie(i("r")),
      g: ie(i("g")),
      b: ie(i("b")),
      a: ie(i("a") * 100) / 100
    };
    return this._c(s);
  }
  /**
   * Mix the color with pure white, from 0 to 100.
   * Providing 0 will do nothing, providing 100 will always return white.
   */
  tint(t = 10) {
    return this.mix({
      r: 255,
      g: 255,
      b: 255,
      a: 1
    }, t);
  }
  /**
   * Mix the color with pure black, from 0 to 100.
   * Providing 0 will do nothing, providing 100 will always return black.
   */
  shade(t = 10) {
    return this.mix({
      r: 0,
      g: 0,
      b: 0,
      a: 1
    }, t);
  }
  onBackground(t) {
    const r = this._c(t), n = this.a + r.a * (1 - this.a), o = (i) => ie((this[i] * this.a + r[i] * r.a * (1 - this.a)) / n);
    return this._c({
      r: o("r"),
      g: o("g"),
      b: o("b"),
      a: n
    });
  }
  // ======================= Status =======================
  isDark() {
    return this.getBrightness() < 128;
  }
  isLight() {
    return this.getBrightness() >= 128;
  }
  // ======================== MISC ========================
  equals(t) {
    return this.r === t.r && this.g === t.g && this.b === t.b && this.a === t.a;
  }
  clone() {
    return this._c(this);
  }
  // ======================= Format =======================
  toHexString() {
    let t = "#";
    const r = (this.r || 0).toString(16);
    t += r.length === 2 ? r : "0" + r;
    const n = (this.g || 0).toString(16);
    t += n.length === 2 ? n : "0" + n;
    const o = (this.b || 0).toString(16);
    if (t += o.length === 2 ? o : "0" + o, typeof this.a == "number" && this.a >= 0 && this.a < 1) {
      const i = ie(this.a * 255).toString(16);
      t += i.length === 2 ? i : "0" + i;
    }
    return t;
  }
  /** CSS support color pattern */
  toHsl() {
    return {
      h: this.getHue(),
      s: this.getSaturation(),
      l: this.getLightness(),
      a: this.a
    };
  }
  /** CSS support color pattern */
  toHslString() {
    const t = this.getHue(), r = ie(this.getSaturation() * 100), n = ie(this.getLightness() * 100);
    return this.a !== 1 ? `hsla(${t},${r}%,${n}%,${this.a})` : `hsl(${t},${r}%,${n}%)`;
  }
  /** Same as toHsb */
  toHsv() {
    return {
      h: this.getHue(),
      s: this.getSaturation(),
      v: this.getValue(),
      a: this.a
    };
  }
  toRgb() {
    return {
      r: this.r,
      g: this.g,
      b: this.b,
      a: this.a
    };
  }
  toRgbString() {
    return this.a !== 1 ? `rgba(${this.r},${this.g},${this.b},${this.a})` : `rgb(${this.r},${this.g},${this.b})`;
  }
  toString() {
    return this.toRgbString();
  }
  // ====================== Privates ======================
  /** Return a new FastColor object with one channel changed */
  _sc(t, r, n) {
    const o = this.clone();
    return o[t] = Le(r, n), o;
  }
  _c(t) {
    return new this.constructor(t);
  }
  getMax() {
    return typeof this._max > "u" && (this._max = Math.max(this.r, this.g, this.b)), this._max;
  }
  getMin() {
    return typeof this._min > "u" && (this._min = Math.min(this.r, this.g, this.b)), this._min;
  }
  fromHexString(t) {
    const r = t.replace("#", "");
    function n(o, i) {
      return parseInt(r[o] + r[i || o], 16);
    }
    r.length < 6 ? (this.r = n(0), this.g = n(1), this.b = n(2), this.a = r[3] ? n(3) / 255 : 1) : (this.r = n(0, 1), this.g = n(2, 3), this.b = n(4, 5), this.a = r[6] ? n(6, 7) / 255 : 1);
  }
  fromHsl({
    h: t,
    s: r,
    l: n,
    a: o
  }) {
    if (this._h = t % 360, this._s = r, this._l = n, this.a = typeof o == "number" ? o : 1, r <= 0) {
      const I = ie(n * 255);
      this.r = I, this.g = I, this.b = I;
    }
    let i = 0, s = 0, f = 0;
    const c = t / 60, l = (1 - Math.abs(2 * n - 1)) * r, g = l * (1 - Math.abs(c % 2 - 1));
    c >= 0 && c < 1 ? (i = l, s = g) : c >= 1 && c < 2 ? (i = g, s = l) : c >= 2 && c < 3 ? (s = l, f = g) : c >= 3 && c < 4 ? (s = g, f = l) : c >= 4 && c < 5 ? (i = g, f = l) : c >= 5 && c < 6 && (i = l, f = g);
    const d = n - l / 2;
    this.r = ie((i + d) * 255), this.g = ie((s + d) * 255), this.b = ie((f + d) * 255);
  }
  fromHsv({
    h: t,
    s: r,
    v: n,
    a: o
  }) {
    this._h = t % 360, this._s = r, this._v = n, this.a = typeof o == "number" ? o : 1;
    const i = ie(n * 255);
    if (this.r = i, this.g = i, this.b = i, r <= 0)
      return;
    const s = t / 60, f = Math.floor(s), c = s - f, l = ie(n * (1 - r) * 255), g = ie(n * (1 - r * c) * 255), d = ie(n * (1 - r * (1 - c)) * 255);
    switch (f) {
      case 0:
        this.g = d, this.b = l;
        break;
      case 1:
        this.r = g, this.b = l;
        break;
      case 2:
        this.r = l, this.b = d;
        break;
      case 3:
        this.r = l, this.g = g;
        break;
      case 4:
        this.r = d, this.g = l;
        break;
      case 5:
      default:
        this.g = l, this.b = g;
        break;
    }
  }
  fromHsvString(t) {
    const r = ht(t, tr);
    this.fromHsv({
      h: r[0],
      s: r[1],
      v: r[2],
      a: r[3]
    });
  }
  fromHslString(t) {
    const r = ht(t, tr);
    this.fromHsl({
      h: r[0],
      s: r[1],
      l: r[2],
      a: r[3]
    });
  }
  fromRgbString(t) {
    const r = ht(t, (n, o) => (
      // Convert percentage to number. e.g. 50% -> 128
      o.includes("%") ? ie(n / 100 * 255) : n
    ));
    this.r = r[0], this.g = r[1], this.b = r[2], this.a = r[3];
  }
}
var qe = 2, rr = 0.16, pa = 0.05, ga = 0.05, ya = 0.15, Or = 5, Tr = 4, ba = [{
  index: 7,
  amount: 15
}, {
  index: 6,
  amount: 25
}, {
  index: 5,
  amount: 30
}, {
  index: 5,
  amount: 45
}, {
  index: 5,
  amount: 65
}, {
  index: 5,
  amount: 85
}, {
  index: 4,
  amount: 90
}, {
  index: 3,
  amount: 95
}, {
  index: 2,
  amount: 97
}, {
  index: 1,
  amount: 98
}];
function nr(e, t, r) {
  var n;
  return Math.round(e.h) >= 60 && Math.round(e.h) <= 240 ? n = r ? Math.round(e.h) - qe * t : Math.round(e.h) + qe * t : n = r ? Math.round(e.h) + qe * t : Math.round(e.h) - qe * t, n < 0 ? n += 360 : n >= 360 && (n -= 360), n;
}
function ar(e, t, r) {
  if (e.h === 0 && e.s === 0)
    return e.s;
  var n;
  return r ? n = e.s - rr * t : t === Tr ? n = e.s + rr : n = e.s + pa * t, n > 1 && (n = 1), r && t === Or && n > 0.1 && (n = 0.1), n < 0.06 && (n = 0.06), Math.round(n * 100) / 100;
}
function or(e, t, r) {
  var n;
  return r ? n = e.v + ga * t : n = e.v - ya * t, n = Math.max(0, Math.min(1, n)), Math.round(n * 100) / 100;
}
function Ea(e) {
  for (var t = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, r = [], n = new De(e), o = n.toHsv(), i = Or; i > 0; i -= 1) {
    var s = new De({
      h: nr(o, i, !0),
      s: ar(o, i, !0),
      v: or(o, i, !0)
    });
    r.push(s);
  }
  r.push(n);
  for (var f = 1; f <= Tr; f += 1) {
    var c = new De({
      h: nr(o, f),
      s: ar(o, f),
      v: or(o, f)
    });
    r.push(c);
  }
  return t.theme === "dark" ? ba.map(function(l) {
    var g = l.index, d = l.amount;
    return new De(t.backgroundColor || "#141414").mix(r[g], d).toHexString();
  }) : r.map(function(l) {
    return l.toHexString();
  });
}
var gt = ["#e6f4ff", "#bae0ff", "#91caff", "#69b1ff", "#4096ff", "#1677ff", "#0958d9", "#003eb3", "#002c8c", "#001d66"];
gt.primary = gt[5];
function ir(e, t) {
  var r = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var n = Object.getOwnPropertySymbols(e);
    t && (n = n.filter(function(o) {
      return Object.getOwnPropertyDescriptor(e, o).enumerable;
    })), r.push.apply(r, n);
  }
  return r;
}
function be(e) {
  for (var t = 1; t < arguments.length; t++) {
    var r = arguments[t] != null ? arguments[t] : {};
    t % 2 ? ir(Object(r), !0).forEach(function(n) {
      le(e, n, r[n]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(r)) : ir(Object(r)).forEach(function(n) {
      Object.defineProperty(e, n, Object.getOwnPropertyDescriptor(r, n));
    });
  }
  return e;
}
function Ca() {
  return !!(typeof window < "u" && window.document && window.document.createElement);
}
function _a(e, t) {
  if (!e)
    return !1;
  if (e.contains)
    return e.contains(t);
  for (var r = t; r; ) {
    if (r === e)
      return !0;
    r = r.parentNode;
  }
  return !1;
}
var sr = "data-rc-order", cr = "data-rc-priority", Oa = "rc-util-key", yt = /* @__PURE__ */ new Map();
function xr() {
  var e = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, t = e.mark;
  return t ? t.startsWith("data-") ? t : "data-".concat(t) : Oa;
}
function Tt(e) {
  if (e.attachTo)
    return e.attachTo;
  var t = document.querySelector("head");
  return t || document.body;
}
function Ta(e) {
  return e === "queue" ? "prependQueue" : e ? "prepend" : "append";
}
function xt(e) {
  return Array.from((yt.get(e) || e).children).filter(function(t) {
    return t.tagName === "STYLE";
  });
}
function wr(e) {
  var t = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
  if (!Ca())
    return null;
  var r = t.csp, n = t.prepend, o = t.priority, i = o === void 0 ? 0 : o, s = Ta(n), f = s === "prependQueue", c = document.createElement("style");
  c.setAttribute(sr, s), f && i && c.setAttribute(cr, "".concat(i)), r != null && r.nonce && (c.nonce = r == null ? void 0 : r.nonce), c.innerHTML = e;
  var l = Tt(t), g = l.firstChild;
  if (n) {
    if (f) {
      var d = (t.styles || xt(l)).filter(function(I) {
        if (!["prepend", "prependQueue"].includes(I.getAttribute(sr)))
          return !1;
        var k = Number(I.getAttribute(cr) || 0);
        return i >= k;
      });
      if (d.length)
        return l.insertBefore(c, d[d.length - 1].nextSibling), c;
    }
    l.insertBefore(c, g);
  } else
    l.appendChild(c);
  return c;
}
function xa(e) {
  var t = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, r = Tt(t);
  return (t.styles || xt(r)).find(function(n) {
    return n.getAttribute(xr(t)) === e;
  });
}
function wa(e, t) {
  var r = yt.get(e);
  if (!r || !_a(document, r)) {
    var n = wr("", t), o = n.parentNode;
    yt.set(e, o), e.removeChild(n);
  }
}
function Sa(e, t) {
  var r = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {}, n = Tt(r), o = xt(n), i = be(be({}, r), {}, {
    styles: o
  });
  wa(n, i);
  var s = xa(t, i);
  if (s) {
    var f, c;
    if ((f = i.csp) !== null && f !== void 0 && f.nonce && s.nonce !== ((c = i.csp) === null || c === void 0 ? void 0 : c.nonce)) {
      var l;
      s.nonce = (l = i.csp) === null || l === void 0 ? void 0 : l.nonce;
    }
    return s.innerHTML !== e && (s.innerHTML = e), s;
  }
  var g = wr(e, i);
  return g.setAttribute(xr(i), t), g;
}
function Sr(e) {
  var t;
  return e == null || (t = e.getRootNode) === null || t === void 0 ? void 0 : t.call(e);
}
function Ra(e) {
  return Sr(e) instanceof ShadowRoot;
}
function Ma(e) {
  return Ra(e) ? Sr(e) : null;
}
var bt = {}, wt = [], $a = function(t) {
  wt.push(t);
};
function Pa(e, t) {
  if (process.env.NODE_ENV !== "production" && !e && console !== void 0) {
    var r = wt.reduce(function(n, o) {
      return o(n ?? "", "warning");
    }, t);
    r && console.error("Warning: ".concat(r));
  }
}
function Na(e, t) {
  if (process.env.NODE_ENV !== "production" && !e && console !== void 0) {
    var r = wt.reduce(function(n, o) {
      return o(n ?? "", "note");
    }, t);
    r && console.warn("Note: ".concat(r));
  }
}
function Da() {
  bt = {};
}
function Rr(e, t, r) {
  !t && !bt[r] && (e(!1, r), bt[r] = !0);
}
function nt(e, t) {
  Rr(Pa, e, t);
}
function Aa(e, t) {
  Rr(Na, e, t);
}
nt.preMessage = $a;
nt.resetWarned = Da;
nt.noteOnce = Aa;
function ja(e) {
  return e.replace(/-(.)/g, function(t, r) {
    return r.toUpperCase();
  });
}
function Ia(e, t) {
  nt(e, "[@ant-design/icons] ".concat(t));
}
function lr(e) {
  return Se(e) === "object" && typeof e.name == "string" && typeof e.theme == "string" && (Se(e.icon) === "object" || typeof e.icon == "function");
}
function ur() {
  var e = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
  return Object.keys(e).reduce(function(t, r) {
    var n = e[r];
    switch (r) {
      case "class":
        t.className = n, delete t.class;
        break;
      default:
        delete t[r], t[ja(r)] = n;
    }
    return t;
  }, {});
}
function Et(e, t, r) {
  return r ? /* @__PURE__ */ Ze.createElement(e.tag, be(be({
    key: t
  }, ur(e.attrs)), r), (e.children || []).map(function(n, o) {
    return Et(n, "".concat(t, "-").concat(e.tag, "-").concat(o));
  })) : /* @__PURE__ */ Ze.createElement(e.tag, be({
    key: t
  }, ur(e.attrs)), (e.children || []).map(function(n, o) {
    return Et(n, "".concat(t, "-").concat(e.tag, "-").concat(o));
  }));
}
function Mr(e) {
  return Ea(e)[0];
}
function $r(e) {
  return e ? Array.isArray(e) ? e : [e] : [];
}
var ka = `
.anticon {
  display: inline-flex;
  align-items: center;
  color: inherit;
  font-style: normal;
  line-height: 0;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.anticon > * {
  line-height: 1;
}

.anticon svg {
  display: inline-block;
}

.anticon::before {
  display: none;
}

.anticon .anticon-icon {
  display: block;
}

.anticon[tabindex] {
  cursor: pointer;
}

.anticon-spin::before,
.anticon-spin {
  display: inline-block;
  -webkit-animation: loadingCircle 1s infinite linear;
  animation: loadingCircle 1s infinite linear;
}

@-webkit-keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
`, La = function(t) {
  var r = $n(br), n = r.csp, o = r.prefixCls, i = r.layer, s = ka;
  o && (s = s.replace(/anticon/g, o)), i && (s = "@layer ".concat(i, ` {
`).concat(s, `
}`)), Qe(function() {
    var f = t.current, c = Ma(f);
    Sa(s, "@ant-design-icons", {
      prepend: !i,
      csp: n,
      attachTo: c
    });
  }, []);
}, Ha = ["icon", "className", "onClick", "style", "primaryColor", "secondaryColor"], Fe = {
  primaryColor: "#333",
  secondaryColor: "#E6E6E6",
  calculated: !1
};
function Fa(e) {
  var t = e.primaryColor, r = e.secondaryColor;
  Fe.primaryColor = t, Fe.secondaryColor = r || Mr(t), Fe.calculated = !!r;
}
function Va() {
  return be({}, Fe);
}
var Ae = function(t) {
  var r = t.icon, n = t.className, o = t.onClick, i = t.style, s = t.primaryColor, f = t.secondaryColor, c = Cr(t, Ha), l = $.useRef(), g = Fe;
  if (s && (g = {
    primaryColor: s,
    secondaryColor: f || Mr(s)
  }), La(l), Ia(lr(r), "icon should be icon definiton, but got ".concat(r)), !lr(r))
    return null;
  var d = r;
  return d && typeof d.icon == "function" && (d = be(be({}, d), {}, {
    icon: d.icon(g.primaryColor, g.secondaryColor)
  })), Et(d.icon, "svg-".concat(d.name), be(be({
    className: n,
    onClick: o,
    style: i,
    "data-icon": d.name,
    width: "1em",
    height: "1em",
    fill: "currentColor",
    "aria-hidden": "true"
  }, c), {}, {
    ref: l
  }));
};
Ae.displayName = "IconReact";
Ae.getTwoToneColors = Va;
Ae.setTwoToneColors = Fa;
function Pr(e) {
  var t = $r(e), r = Er(t, 2), n = r[0], o = r[1];
  return Ae.setTwoToneColors({
    primaryColor: n,
    secondaryColor: o
  });
}
function za() {
  var e = Ae.getTwoToneColors();
  return e.calculated ? [e.primaryColor, e.secondaryColor] : e.primaryColor;
}
var Ya = ["className", "icon", "spin", "rotate", "tabIndex", "onClick", "twoToneColor"];
Pr(gt.primary);
var te = /* @__PURE__ */ $.forwardRef(function(e, t) {
  var r = e.className, n = e.icon, o = e.spin, i = e.rotate, s = e.tabIndex, f = e.onClick, c = e.twoToneColor, l = Cr(e, Ya), g = $.useContext(br), d = g.prefixCls, I = d === void 0 ? "anticon" : d, k = g.rootClassName, T = va(k, I, le(le({}, "".concat(I, "-").concat(n.name), !!n.name), "".concat(I, "-spin"), !!o || n.name === "loading"), r), x = s;
  x === void 0 && f && (x = -1);
  var N = i ? {
    msTransform: "rotate(".concat(i, "deg)"),
    transform: "rotate(".concat(i, "deg)")
  } : void 0, E = $r(c), C = Er(E, 2), b = C[0], M = C[1];
  return /* @__PURE__ */ $.createElement("span", ee({
    role: "img",
    "aria-label": n.name
  }, l, {
    ref: t,
    tabIndex: x,
    onClick: f,
    className: T
  }), /* @__PURE__ */ $.createElement(Ae, {
    icon: n,
    primaryColor: b,
    secondaryColor: M,
    style: N
  }));
});
te.displayName = "AntdIcon";
te.getTwoToneColor = za;
te.setTwoToneColor = Pr;
var Ba = { icon: { tag: "svg", attrs: { viewBox: "64 64 896 896", focusable: "false" }, children: [{ tag: "path", attrs: { d: "M908 640H804V488c0-4.4-3.6-8-8-8H548v-96h108c8.8 0 16-7.2 16-16V80c0-8.8-7.2-16-16-16H368c-8.8 0-16 7.2-16 16v288c0 8.8 7.2 16 16 16h108v96H228c-4.4 0-8 3.6-8 8v152H116c-8.8 0-16 7.2-16 16v288c0 8.8 7.2 16 16 16h288c8.8 0 16-7.2 16-16V656c0-8.8-7.2-16-16-16H292v-88h440v88H620c-8.8 0-16 7.2-16 16v288c0 8.8 7.2 16 16 16h288c8.8 0 16-7.2 16-16V656c0-8.8-7.2-16-16-16zm-564 76v168H176V716h168zm84-408V140h168v168H428zm420 576H680V716h168v168z" } }] }, name: "apartment", theme: "outlined" }, Ua = function(t, r) {
  return /* @__PURE__ */ $.createElement(te, ee({}, t, {
    ref: r,
    icon: Ba
  }));
}, Nr = /* @__PURE__ */ $.forwardRef(Ua);
process.env.NODE_ENV !== "production" && (Nr.displayName = "ApartmentOutlined");
var Wa = { icon: { tag: "svg", attrs: { viewBox: "64 64 896 896", focusable: "false" }, children: [{ tag: "path", attrs: { d: "M862 465.3h-81c-4.6 0-9 2-12.1 5.5L550 723.1V160c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v563.1L255.1 470.8c-3-3.5-7.4-5.5-12.1-5.5h-81c-6.8 0-10.5 8.1-6 13.2L487.9 861a31.96 31.96 0 0048.3 0L868 478.5c4.5-5.2.8-13.2-6-13.2z" } }] }, name: "arrow-down", theme: "outlined" }, Ka = function(t, r) {
  return /* @__PURE__ */ $.createElement(te, ee({}, t, {
    ref: r,
    icon: Wa
  }));
}, Dr = /* @__PURE__ */ $.forwardRef(Ka);
process.env.NODE_ENV !== "production" && (Dr.displayName = "ArrowDownOutlined");
var Ga = { icon: { tag: "svg", attrs: { viewBox: "64 64 896 896", focusable: "false" }, children: [{ tag: "path", attrs: { d: "M868 545.5L536.1 163a31.96 31.96 0 00-48.3 0L156 545.5a7.97 7.97 0 006 13.2h81c4.6 0 9-2 12.1-5.5L474 300.9V864c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V300.9l218.9 252.3c3 3.5 7.4 5.5 12.1 5.5h81c6.8 0 10.5-8 6-13.2z" } }] }, name: "arrow-up", theme: "outlined" }, qa = function(t, r) {
  return /* @__PURE__ */ $.createElement(te, ee({}, t, {
    ref: r,
    icon: Ga
  }));
}, Ar = /* @__PURE__ */ $.forwardRef(qa);
process.env.NODE_ENV !== "production" && (Ar.displayName = "ArrowUpOutlined");
var Ja = { icon: { tag: "svg", attrs: { "fill-rule": "evenodd", viewBox: "64 64 896 896", focusable: "false" }, children: [{ tag: "path", attrs: { d: "M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z" } }] }, name: "close", theme: "outlined" }, Xa = function(t, r) {
  return /* @__PURE__ */ $.createElement(te, ee({}, t, {
    ref: r,
    icon: Ja
  }));
}, jr = /* @__PURE__ */ $.forwardRef(Xa);
process.env.NODE_ENV !== "production" && (jr.displayName = "CloseOutlined");
var Za = { icon: { tag: "svg", attrs: { viewBox: "64 64 896 896", focusable: "false" }, children: [{ tag: "path", attrs: { d: "M888 680h-54V540H546v-92h238c8.8 0 16-7.2 16-16V168c0-8.8-7.2-16-16-16H240c-8.8 0-16 7.2-16 16v264c0 8.8 7.2 16 16 16h238v92H190v140h-54c-4.4 0-8 3.6-8 8v176c0 4.4 3.6 8 8 8h176c4.4 0 8-3.6 8-8V688c0-4.4-3.6-8-8-8h-54v-72h220v72h-54c-4.4 0-8 3.6-8 8v176c0 4.4 3.6 8 8 8h176c4.4 0 8-3.6 8-8V688c0-4.4-3.6-8-8-8h-54v-72h220v72h-54c-4.4 0-8 3.6-8 8v176c0 4.4 3.6 8 8 8h176c4.4 0 8-3.6 8-8V688c0-4.4-3.6-8-8-8zM256 805.3c0 1.5-1.2 2.7-2.7 2.7h-58.7c-1.5 0-2.7-1.2-2.7-2.7v-58.7c0-1.5 1.2-2.7 2.7-2.7h58.7c1.5 0 2.7 1.2 2.7 2.7v58.7zm288 0c0 1.5-1.2 2.7-2.7 2.7h-58.7c-1.5 0-2.7-1.2-2.7-2.7v-58.7c0-1.5 1.2-2.7 2.7-2.7h58.7c1.5 0 2.7 1.2 2.7 2.7v58.7zM288 384V216h448v168H288zm544 421.3c0 1.5-1.2 2.7-2.7 2.7h-58.7c-1.5 0-2.7-1.2-2.7-2.7v-58.7c0-1.5 1.2-2.7 2.7-2.7h58.7c1.5 0 2.7 1.2 2.7 2.7v58.7zM360 300a40 40 0 1080 0 40 40 0 10-80 0z" } }] }, name: "cluster", theme: "outlined" }, Qa = function(t, r) {
  return /* @__PURE__ */ $.createElement(te, ee({}, t, {
    ref: r,
    icon: Za
  }));
}, Ir = /* @__PURE__ */ $.forwardRef(Qa);
process.env.NODE_ENV !== "production" && (Ir.displayName = "ClusterOutlined");
var eo = { icon: { tag: "svg", attrs: { viewBox: "64 64 896 896", focusable: "false" }, children: [{ tag: "path", attrs: { d: "M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z" } }] }, name: "copy", theme: "outlined" }, to = function(t, r) {
  return /* @__PURE__ */ $.createElement(te, ee({}, t, {
    ref: r,
    icon: eo
  }));
}, kr = /* @__PURE__ */ $.forwardRef(to);
process.env.NODE_ENV !== "production" && (kr.displayName = "CopyOutlined");
var ro = { icon: { tag: "svg", attrs: { viewBox: "64 64 896 896", focusable: "false" }, children: [{ tag: "path", attrs: { d: "M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z" } }] }, name: "delete", theme: "outlined" }, no = function(t, r) {
  return /* @__PURE__ */ $.createElement(te, ee({}, t, {
    ref: r,
    icon: ro
  }));
}, Lr = /* @__PURE__ */ $.forwardRef(no);
process.env.NODE_ENV !== "production" && (Lr.displayName = "DeleteOutlined");
var ao = { icon: { tag: "svg", attrs: { viewBox: "64 64 896 896", focusable: "false" }, children: [{ tag: "path", attrs: { d: "M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z" } }] }, name: "download", theme: "outlined" }, oo = function(t, r) {
  return /* @__PURE__ */ $.createElement(te, ee({}, t, {
    ref: r,
    icon: ao
  }));
}, Hr = /* @__PURE__ */ $.forwardRef(oo);
process.env.NODE_ENV !== "production" && (Hr.displayName = "DownloadOutlined");
var io = { icon: { tag: "svg", attrs: { viewBox: "64 64 896 896", focusable: "false" }, children: [{ tag: "path", attrs: { d: "M942.2 486.2C847.4 286.5 704.1 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 000 51.5C176.6 737.5 319.9 838 512 838c192.2 0 335.4-100.5 430.2-300.3 7.7-16.2 7.7-35 0-51.5zM512 766c-161.3 0-279.4-81.8-362.7-254C232.6 339.8 350.7 258 512 258c161.3 0 279.4 81.8 362.7 254C791.5 684.2 673.4 766 512 766zm-4-430c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z" } }] }, name: "eye", theme: "outlined" }, so = function(t, r) {
  return /* @__PURE__ */ $.createElement(te, ee({}, t, {
    ref: r,
    icon: io
  }));
}, Fr = /* @__PURE__ */ $.forwardRef(so);
process.env.NODE_ENV !== "production" && (Fr.displayName = "EyeOutlined");
var co = { icon: { tag: "svg", attrs: { viewBox: "64 64 896 896", focusable: "false" }, children: [{ tag: "defs", attrs: {}, children: [{ tag: "style", attrs: {} }] }, { tag: "path", attrs: { d: "M945 412H689c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h256c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zM811 548H689c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h122c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zM477.3 322.5H434c-6.2 0-11.2 5-11.2 11.2v248c0 3.6 1.7 6.9 4.6 9l148.9 108.6c5 3.6 12 2.6 15.6-2.4l25.7-35.1v-.1c3.6-5 2.5-12-2.5-15.6l-126.7-91.6V333.7c.1-6.2-5-11.2-11.1-11.2z" } }, { tag: "path", attrs: { d: "M804.8 673.9H747c-5.6 0-10.9 2.9-13.9 7.7a321 321 0 01-44.5 55.7 317.17 317.17 0 01-101.3 68.3c-39.3 16.6-81 25-124 25-43.1 0-84.8-8.4-124-25-37.9-16-72-39-101.3-68.3s-52.3-63.4-68.3-101.3c-16.6-39.2-25-80.9-25-124 0-43.1 8.4-84.7 25-124 16-37.9 39-72 68.3-101.3 29.3-29.3 63.4-52.3 101.3-68.3 39.2-16.6 81-25 124-25 43.1 0 84.8 8.4 124 25 37.9 16 72 39 101.3 68.3a321 321 0 0144.5 55.7c3 4.8 8.3 7.7 13.9 7.7h57.8c6.9 0 11.3-7.2 8.2-13.3-65.2-129.7-197.4-214-345-215.7-216.1-2.7-395.6 174.2-396 390.1C71.6 727.5 246.9 903 463.2 903c149.5 0 283.9-84.6 349.8-215.8a9.18 9.18 0 00-8.2-13.3z" } }] }, name: "field-time", theme: "outlined" }, lo = function(t, r) {
  return /* @__PURE__ */ $.createElement(te, ee({}, t, {
    ref: r,
    icon: co
  }));
}, Vr = /* @__PURE__ */ $.forwardRef(lo);
process.env.NODE_ENV !== "production" && (Vr.displayName = "FieldTimeOutlined");
var uo = { icon: { tag: "svg", attrs: { viewBox: "64 64 896 896", focusable: "false" }, children: [{ tag: "path", attrs: { d: "M688 312v-48c0-4.4-3.6-8-8-8H296c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h384c4.4 0 8-3.6 8-8zm-392 88c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H296zm144 452H208V148h560v344c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V108c0-17.7-14.3-32-32-32H168c-17.7 0-32 14.3-32 32v784c0 17.7 14.3 32 32 32h272c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm445.7 51.5l-93.3-93.3C814.7 780.7 828 743.9 828 704c0-97.2-78.8-176-176-176s-176 78.8-176 176 78.8 176 176 176c35.8 0 69-10.7 96.8-29l94.7 94.7c1.6 1.6 3.6 2.3 5.6 2.3s4.1-.8 5.6-2.3l31-31a7.9 7.9 0 000-11.2zM652 816c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z" } }] }, name: "file-search", theme: "outlined" }, fo = function(t, r) {
  return /* @__PURE__ */ $.createElement(te, ee({}, t, {
    ref: r,
    icon: uo
  }));
}, zr = /* @__PURE__ */ $.forwardRef(fo);
process.env.NODE_ENV !== "production" && (zr.displayName = "FileSearchOutlined");
var ho = { icon: { tag: "svg", attrs: { viewBox: "64 64 896 896", focusable: "false" }, children: [{ tag: "path", attrs: { d: "M904 512h-56c-4.4 0-8 3.6-8 8v320H184V184h320c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V520c0-4.4-3.6-8-8-8z" } }, { tag: "path", attrs: { d: "M355.9 534.9L354 653.8c-.1 8.9 7.1 16.2 16 16.2h.4l118-2.9c2-.1 4-.9 5.4-2.3l415.9-415c3.1-3.1 3.1-8.2 0-11.3L785.4 114.3c-1.6-1.6-3.6-2.3-5.7-2.3s-4.1.8-5.7 2.3l-415.8 415a8.3 8.3 0 00-2.3 5.6zm63.5 23.6L779.7 199l45.2 45.1-360.5 359.7-45.7 1.1.7-46.4z" } }] }, name: "form", theme: "outlined" }, mo = function(t, r) {
  return /* @__PURE__ */ $.createElement(te, ee({}, t, {
    ref: r,
    icon: ho
  }));
}, St = /* @__PURE__ */ $.forwardRef(mo);
process.env.NODE_ENV !== "production" && (St.displayName = "FormOutlined");
var vo = { icon: { tag: "svg", attrs: { viewBox: "64 64 896 896", focusable: "false" }, children: [{ tag: "path", attrs: { d: "M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM142.4 642.1L298.7 519a8.84 8.84 0 000-13.9L142.4 381.9c-5.8-4.6-14.4-.5-14.4 6.9v246.3a8.9 8.9 0 0014.4 7z" } }] }, name: "menu-unfold", theme: "outlined" }, po = function(t, r) {
  return /* @__PURE__ */ $.createElement(te, ee({}, t, {
    ref: r,
    icon: vo
  }));
}, Yr = /* @__PURE__ */ $.forwardRef(po);
process.env.NODE_ENV !== "production" && (Yr.displayName = "MenuUnfoldOutlined");
var go = { icon: { tag: "svg", attrs: { viewBox: "64 64 896 896", focusable: "false" }, children: [{ tag: "path", attrs: { d: "M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z" } }, { tag: "path", attrs: { d: "M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z" } }] }, name: "plus", theme: "outlined" }, yo = function(t, r) {
  return /* @__PURE__ */ $.createElement(te, ee({}, t, {
    ref: r,
    icon: go
  }));
}, Br = /* @__PURE__ */ $.forwardRef(yo);
process.env.NODE_ENV !== "production" && (Br.displayName = "PlusOutlined");
var bo = { icon: { tag: "svg", attrs: { viewBox: "64 64 896 896", focusable: "false" }, children: [{ tag: "path", attrs: { d: "M758.2 839.1C851.8 765.9 912 651.9 912 523.9 912 303 733.5 124.3 512.6 124 291.4 123.7 112 302.8 112 523.9c0 125.2 57.5 236.9 147.6 310.2 3.5 2.8 8.6 2.2 11.4-1.3l39.4-50.5c2.7-3.4 2.1-8.3-1.2-11.1-8.1-6.6-15.9-13.7-23.4-21.2a318.64 318.64 0 01-68.6-101.7C200.4 609 192 567.1 192 523.9s8.4-85.1 25.1-124.5c16.1-38.1 39.2-72.3 68.6-101.7 29.4-29.4 63.6-52.5 101.7-68.6C426.9 212.4 468.8 204 512 204s85.1 8.4 124.5 25.1c38.1 16.1 72.3 39.2 101.7 68.6 29.4 29.4 52.5 63.6 68.6 101.7 16.7 39.4 25.1 81.3 25.1 124.5s-8.4 85.1-25.1 124.5a318.64 318.64 0 01-68.6 101.7c-9.3 9.3-19.1 18-29.3 26L668.2 724a8 8 0 00-14.1 3l-39.6 162.2c-1.2 5 2.6 9.9 7.7 9.9l167 .8c6.7 0 10.5-7.7 6.3-12.9l-37.3-47.9z" } }] }, name: "redo", theme: "outlined" }, Eo = function(t, r) {
  return /* @__PURE__ */ $.createElement(te, ee({}, t, {
    ref: r,
    icon: bo
  }));
}, Ur = /* @__PURE__ */ $.forwardRef(Eo);
process.env.NODE_ENV !== "production" && (Ur.displayName = "RedoOutlined");
var Co = { icon: { tag: "svg", attrs: { viewBox: "64 64 896 896", focusable: "false" }, children: [{ tag: "path", attrs: { d: "M893.3 293.3L730.7 130.7c-7.5-7.5-16.7-13-26.7-16V112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V338.5c0-17-6.7-33.2-18.7-45.2zM384 184h256v104H384V184zm456 656H184V184h136v136c0 17.7 14.3 32 32 32h320c17.7 0 32-14.3 32-32V205.8l136 136V840zM512 442c-79.5 0-144 64.5-144 144s64.5 144 144 144 144-64.5 144-144-64.5-144-144-144zm0 224c-44.2 0-80-35.8-80-80s35.8-80 80-80 80 35.8 80 80-35.8 80-80 80z" } }] }, name: "save", theme: "outlined" }, _o = function(t, r) {
  return /* @__PURE__ */ $.createElement(te, ee({}, t, {
    ref: r,
    icon: Co
  }));
}, Wr = /* @__PURE__ */ $.forwardRef(_o);
process.env.NODE_ENV !== "production" && (Wr.displayName = "SaveOutlined");
var Oo = { icon: { tag: "svg", attrs: { viewBox: "64 64 896 896", focusable: "false" }, children: [{ tag: "path", attrs: { d: "M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z" } }] }, name: "search", theme: "outlined" }, To = function(t, r) {
  return /* @__PURE__ */ $.createElement(te, ee({}, t, {
    ref: r,
    icon: Oo
  }));
}, Rt = /* @__PURE__ */ $.forwardRef(To);
process.env.NODE_ENV !== "production" && (Rt.displayName = "SearchOutlined");
var xo = { icon: { tag: "svg", attrs: { viewBox: "64 64 896 896", focusable: "false" }, children: [{ tag: "defs", attrs: {}, children: [{ tag: "style", attrs: {} }] }, { tag: "path", attrs: { d: "M931.4 498.9L94.9 79.5c-3.4-1.7-7.3-2.1-11-1.2a15.99 15.99 0 00-11.7 19.3l86.2 352.2c1.3 5.3 5.2 9.6 10.4 11.3l147.7 50.7-147.6 50.7c-5.2 1.8-9.1 6-10.3 11.3L72.2 926.5c-.9 3.7-.5 7.6 1.2 10.9 3.9 7.9 13.5 11.1 21.5 7.2l836.5-417c3.1-1.5 5.6-4.1 7.2-7.1 3.9-8 .7-17.6-7.2-21.6zM170.8 826.3l50.3-205.6 295.2-101.3c2.3-.8 4.2-2.6 5-5 1.4-4.2-.8-8.7-5-10.2L221.1 403 171 198.2l628 314.9-628.2 313.2z" } }] }, name: "send", theme: "outlined" }, wo = function(t, r) {
  return /* @__PURE__ */ $.createElement(te, ee({}, t, {
    ref: r,
    icon: xo
  }));
}, Kr = /* @__PURE__ */ $.forwardRef(wo);
process.env.NODE_ENV !== "production" && (Kr.displayName = "SendOutlined");
var So = { icon: { tag: "svg", attrs: { viewBox: "64 64 896 896", focusable: "false" }, children: [{ tag: "path", attrs: { d: "M400 317.7h73.9V656c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V317.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 163a8 8 0 00-12.6 0l-112 141.7c-4.1 5.3-.4 13 6.3 13zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z" } }] }, name: "upload", theme: "outlined" }, Ro = function(t, r) {
  return /* @__PURE__ */ $.createElement(te, ee({}, t, {
    ref: r,
    icon: So
  }));
}, Gr = /* @__PURE__ */ $.forwardRef(Ro);
process.env.NODE_ENV !== "production" && (Gr.displayName = "UploadOutlined");
var qr = { exports: {} };
(function(e, t) {
  (function(r, n) {
    e.exports = n();
  })(Jn, function() {
    var r = 1e3, n = 6e4, o = 36e5, i = "millisecond", s = "second", f = "minute", c = "hour", l = "day", g = "week", d = "month", I = "quarter", k = "year", T = "date", x = "Invalid Date", N = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/, E = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g, C = { name: "en", weekdays: "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"), months: "January_February_March_April_May_June_July_August_September_October_November_December".split("_"), ordinal: function(m) {
      var v = ["th", "st", "nd", "rd"], p = m % 100;
      return "[" + m + (v[(p - 20) % 10] || v[p] || v[0]) + "]";
    } }, b = function(m, v, p) {
      var w = String(m);
      return !w || w.length >= v ? m : "" + Array(v + 1 - w.length).join(p) + m;
    }, M = { s: b, z: function(m) {
      var v = -m.utcOffset(), p = Math.abs(v), w = Math.floor(p / 60), y = p % 60;
      return (v <= 0 ? "+" : "-") + b(w, 2, "0") + ":" + b(y, 2, "0");
    }, m: function m(v, p) {
      if (v.date() < p.date()) return -m(p, v);
      var w = 12 * (p.year() - v.year()) + (p.month() - v.month()), y = v.clone().add(w, d), A = p - y < 0, D = v.clone().add(w + (A ? -1 : 1), d);
      return +(-(w + (p - y) / (A ? y - D : D - y)) || 0);
    }, a: function(m) {
      return m < 0 ? Math.ceil(m) || 0 : Math.floor(m);
    }, p: function(m) {
      return { M: d, y: k, w: g, d: l, D: T, h: c, m: f, s, ms: i, Q: I }[m] || String(m || "").toLowerCase().replace(/s$/, "");
    }, u: function(m) {
      return m === void 0;
    } }, u = "en", H = {};
    H[u] = C;
    var V = "$isDayjsObject", J = function(m) {
      return m instanceof B || !(!m || !m[V]);
    }, R = function m(v, p, w) {
      var y;
      if (!v) return u;
      if (typeof v == "string") {
        var A = v.toLowerCase();
        H[A] && (y = A), p && (H[A] = p, y = A);
        var D = v.split("-");
        if (!y && D.length > 1) return m(D[0]);
      } else {
        var U = v.name;
        H[U] = v, y = U;
      }
      return !w && y && (u = y), y || !w && u;
    }, P = function(m, v) {
      if (J(m)) return m.clone();
      var p = typeof v == "object" ? v : {};
      return p.date = m, p.args = arguments, new B(p);
    }, L = M;
    L.l = R, L.i = J, L.w = function(m, v) {
      return P(m, { locale: v.$L, utc: v.$u, x: v.$x, $offset: v.$offset });
    };
    var B = function() {
      function m(p) {
        this.$L = R(p.locale, null, !0), this.parse(p), this.$x = this.$x || p.x || {}, this[V] = !0;
      }
      var v = m.prototype;
      return v.parse = function(p) {
        this.$d = function(w) {
          var y = w.date, A = w.utc;
          if (y === null) return /* @__PURE__ */ new Date(NaN);
          if (L.u(y)) return /* @__PURE__ */ new Date();
          if (y instanceof Date) return new Date(y);
          if (typeof y == "string" && !/Z$/i.test(y)) {
            var D = y.match(N);
            if (D) {
              var U = D[2] - 1 || 0, Z = (D[7] || "0").substring(0, 3);
              return A ? new Date(Date.UTC(D[1], U, D[3] || 1, D[4] || 0, D[5] || 0, D[6] || 0, Z)) : new Date(D[1], U, D[3] || 1, D[4] || 0, D[5] || 0, D[6] || 0, Z);
            }
          }
          return new Date(y);
        }(p), this.init();
      }, v.init = function() {
        var p = this.$d;
        this.$y = p.getFullYear(), this.$M = p.getMonth(), this.$D = p.getDate(), this.$W = p.getDay(), this.$H = p.getHours(), this.$m = p.getMinutes(), this.$s = p.getSeconds(), this.$ms = p.getMilliseconds();
      }, v.$utils = function() {
        return L;
      }, v.isValid = function() {
        return this.$d.toString() !== x;
      }, v.isSame = function(p, w) {
        var y = P(p);
        return this.startOf(w) <= y && y <= this.endOf(w);
      }, v.isAfter = function(p, w) {
        return P(p) < this.startOf(w);
      }, v.isBefore = function(p, w) {
        return this.endOf(w) < P(p);
      }, v.$g = function(p, w, y) {
        return L.u(p) ? this[w] : this.set(y, p);
      }, v.unix = function() {
        return Math.floor(this.valueOf() / 1e3);
      }, v.valueOf = function() {
        return this.$d.getTime();
      }, v.startOf = function(p, w) {
        var y = this, A = !!L.u(w) || w, D = L.p(p), U = function(de, re) {
          var pe = L.w(y.$u ? Date.UTC(y.$y, re, de) : new Date(y.$y, re, de), y);
          return A ? pe : pe.endOf(l);
        }, Z = function(de, re) {
          return L.w(y.toDate()[de].apply(y.toDate("s"), (A ? [0, 0, 0, 0] : [23, 59, 59, 999]).slice(re)), y);
        }, Q = this.$W, ae = this.$M, se = this.$D, me = "set" + (this.$u ? "UTC" : "");
        switch (D) {
          case k:
            return A ? U(1, 0) : U(31, 11);
          case d:
            return A ? U(1, ae) : U(0, ae + 1);
          case g:
            var fe = this.$locale().weekStart || 0, ve = (Q < fe ? Q + 7 : Q) - fe;
            return U(A ? se - ve : se + (6 - ve), ae);
          case l:
          case T:
            return Z(me + "Hours", 0);
          case c:
            return Z(me + "Minutes", 1);
          case f:
            return Z(me + "Seconds", 2);
          case s:
            return Z(me + "Milliseconds", 3);
          default:
            return this.clone();
        }
      }, v.endOf = function(p) {
        return this.startOf(p, !1);
      }, v.$set = function(p, w) {
        var y, A = L.p(p), D = "set" + (this.$u ? "UTC" : ""), U = (y = {}, y[l] = D + "Date", y[T] = D + "Date", y[d] = D + "Month", y[k] = D + "FullYear", y[c] = D + "Hours", y[f] = D + "Minutes", y[s] = D + "Seconds", y[i] = D + "Milliseconds", y)[A], Z = A === l ? this.$D + (w - this.$W) : w;
        if (A === d || A === k) {
          var Q = this.clone().set(T, 1);
          Q.$d[U](Z), Q.init(), this.$d = Q.set(T, Math.min(this.$D, Q.daysInMonth())).$d;
        } else U && this.$d[U](Z);
        return this.init(), this;
      }, v.set = function(p, w) {
        return this.clone().$set(p, w);
      }, v.get = function(p) {
        return this[L.p(p)]();
      }, v.add = function(p, w) {
        var y, A = this;
        p = Number(p);
        var D = L.p(w), U = function(ae) {
          var se = P(A);
          return L.w(se.date(se.date() + Math.round(ae * p)), A);
        };
        if (D === d) return this.set(d, this.$M + p);
        if (D === k) return this.set(k, this.$y + p);
        if (D === l) return U(1);
        if (D === g) return U(7);
        var Z = (y = {}, y[f] = n, y[c] = o, y[s] = r, y)[D] || 1, Q = this.$d.getTime() + p * Z;
        return L.w(Q, this);
      }, v.subtract = function(p, w) {
        return this.add(-1 * p, w);
      }, v.format = function(p) {
        var w = this, y = this.$locale();
        if (!this.isValid()) return y.invalidDate || x;
        var A = p || "YYYY-MM-DDTHH:mm:ssZ", D = L.z(this), U = this.$H, Z = this.$m, Q = this.$M, ae = y.weekdays, se = y.months, me = y.meridiem, fe = function(re, pe, S, ye) {
          return re && (re[pe] || re(w, A)) || S[pe].slice(0, ye);
        }, ve = function(re) {
          return L.s(U % 12 || 12, re, "0");
        }, de = me || function(re, pe, S) {
          var ye = re < 12 ? "AM" : "PM";
          return S ? ye.toLowerCase() : ye;
        };
        return A.replace(E, function(re, pe) {
          return pe || function(S) {
            switch (S) {
              case "YY":
                return String(w.$y).slice(-2);
              case "YYYY":
                return L.s(w.$y, 4, "0");
              case "M":
                return Q + 1;
              case "MM":
                return L.s(Q + 1, 2, "0");
              case "MMM":
                return fe(y.monthsShort, Q, se, 3);
              case "MMMM":
                return fe(se, Q);
              case "D":
                return w.$D;
              case "DD":
                return L.s(w.$D, 2, "0");
              case "d":
                return String(w.$W);
              case "dd":
                return fe(y.weekdaysMin, w.$W, ae, 2);
              case "ddd":
                return fe(y.weekdaysShort, w.$W, ae, 3);
              case "dddd":
                return ae[w.$W];
              case "H":
                return String(U);
              case "HH":
                return L.s(U, 2, "0");
              case "h":
                return ve(1);
              case "hh":
                return ve(2);
              case "a":
                return de(U, Z, !0);
              case "A":
                return de(U, Z, !1);
              case "m":
                return String(Z);
              case "mm":
                return L.s(Z, 2, "0");
              case "s":
                return String(w.$s);
              case "ss":
                return L.s(w.$s, 2, "0");
              case "SSS":
                return L.s(w.$ms, 3, "0");
              case "Z":
                return D;
            }
            return null;
          }(re) || D.replace(":", "");
        });
      }, v.utcOffset = function() {
        return 15 * -Math.round(this.$d.getTimezoneOffset() / 15);
      }, v.diff = function(p, w, y) {
        var A, D = this, U = L.p(w), Z = P(p), Q = (Z.utcOffset() - this.utcOffset()) * n, ae = this - Z, se = function() {
          return L.m(D, Z);
        };
        switch (U) {
          case k:
            A = se() / 12;
            break;
          case d:
            A = se();
            break;
          case I:
            A = se() / 3;
            break;
          case g:
            A = (ae - Q) / 6048e5;
            break;
          case l:
            A = (ae - Q) / 864e5;
            break;
          case c:
            A = ae / o;
            break;
          case f:
            A = ae / n;
            break;
          case s:
            A = ae / r;
            break;
          default:
            A = ae;
        }
        return y ? A : L.a(A);
      }, v.daysInMonth = function() {
        return this.endOf(d).$D;
      }, v.$locale = function() {
        return H[this.$L];
      }, v.locale = function(p, w) {
        if (!p) return this.$L;
        var y = this.clone(), A = R(p, w, !0);
        return A && (y.$L = A), y;
      }, v.clone = function() {
        return L.w(this.$d, this);
      }, v.toDate = function() {
        return new Date(this.valueOf());
      }, v.toJSON = function() {
        return this.isValid() ? this.toISOString() : null;
      }, v.toISOString = function() {
        return this.$d.toISOString();
      }, v.toString = function() {
        return this.$d.toUTCString();
      }, m;
    }(), Y = B.prototype;
    return P.prototype = Y, [["$ms", i], ["$s", s], ["$m", f], ["$H", c], ["$W", l], ["$M", d], ["$y", k], ["$D", T]].forEach(function(m) {
      Y[m[1]] = function(v) {
        return this.$g(v, m[0], m[1]);
      };
    }), P.extend = function(m, v) {
      return m.$i || (m(v, B, P), m.$i = !0), P;
    }, P.locale = R, P.isDayjs = J, P.unix = function(m) {
      return P(1e3 * m);
    }, P.en = H[u], P.Ls = H, P.p = {}, P;
  });
})(qr);
var Mo = qr.exports;
const fr = /* @__PURE__ */ yr(Mo);
var et = function() {
  return et = Object.assign || function(t) {
    for (var r, n = 1, o = arguments.length; n < o; n++) {
      r = arguments[n];
      for (var i in r) Object.prototype.hasOwnProperty.call(r, i) && (t[i] = r[i]);
    }
    return t;
  }, et.apply(this, arguments);
};
function tt(e, t) {
  var r = {};
  for (var n in e) Object.prototype.hasOwnProperty.call(e, n) && t.indexOf(n) < 0 && (r[n] = e[n]);
  if (e != null && typeof Object.getOwnPropertySymbols == "function")
    for (var o = 0, n = Object.getOwnPropertySymbols(e); o < n.length; o++)
      t.indexOf(n[o]) < 0 && Object.prototype.propertyIsEnumerable.call(e, n[o]) && (r[n[o]] = e[n[o]]);
  return r;
}
function ze(e, t, r) {
  if (r || arguments.length === 2) for (var n = 0, o = t.length, i; n < o; n++)
    (i || !(n in t)) && (i || (i = Array.prototype.slice.call(t, 0, n)), i[n] = t[n]);
  return e.concat(i || Array.prototype.slice.call(t));
}
function Ye(e, t) {
  var r = t && t.cache ? t.cache : jo, n = t && t.serializer ? t.serializer : Ao, o = t && t.strategy ? t.strategy : No;
  return o(e, {
    cache: r,
    serializer: n
  });
}
function $o(e) {
  return e == null || typeof e == "number" || typeof e == "boolean";
}
function Po(e, t, r, n) {
  var o = $o(n) ? n : r(n), i = t.get(o);
  return typeof i > "u" && (i = e.call(this, n), t.set(o, i)), i;
}
function Jr(e, t, r) {
  var n = Array.prototype.slice.call(arguments, 3), o = r(n), i = t.get(o);
  return typeof i > "u" && (i = e.apply(this, n), t.set(o, i)), i;
}
function Xr(e, t, r, n, o) {
  return r.bind(t, e, n, o);
}
function No(e, t) {
  var r = e.length === 1 ? Po : Jr;
  return Xr(e, this, r, t.cache.create(), t.serializer);
}
function Do(e, t) {
  return Xr(e, this, Jr, t.cache.create(), t.serializer);
}
var Ao = function() {
  return JSON.stringify(arguments);
};
function Mt() {
  this.cache = /* @__PURE__ */ Object.create(null);
}
Mt.prototype.get = function(e) {
  return this.cache[e];
};
Mt.prototype.set = function(e, t) {
  this.cache[e] = t;
};
var jo = {
  create: function() {
    return new Mt();
  }
}, Be = {
  variadic: Do
};
function Io(e, t, r) {
  if (r === void 0 && (r = Error), !e)
    throw new r(t);
}
Ye(function() {
  for (var e, t = [], r = 0; r < arguments.length; r++)
    t[r] = arguments[r];
  return new ((e = Intl.NumberFormat).bind.apply(e, ze([void 0], t, !1)))();
}, {
  strategy: Be.variadic
});
Ye(function() {
  for (var e, t = [], r = 0; r < arguments.length; r++)
    t[r] = arguments[r];
  return new ((e = Intl.DateTimeFormat).bind.apply(e, ze([void 0], t, !1)))();
}, {
  strategy: Be.variadic
});
Ye(function() {
  for (var e, t = [], r = 0; r < arguments.length; r++)
    t[r] = arguments[r];
  return new ((e = Intl.PluralRules).bind.apply(e, ze([void 0], t, !1)))();
}, {
  strategy: Be.variadic
});
Ye(function() {
  for (var e, t = [], r = 0; r < arguments.length; r++)
    t[r] = arguments[r];
  return new ((e = Intl.Locale).bind.apply(e, ze([void 0], t, !1)))();
}, {
  strategy: Be.variadic
});
Ye(function() {
  for (var e, t = [], r = 0; r < arguments.length; r++)
    t[r] = arguments[r];
  return new ((e = Intl.ListFormat).bind.apply(e, ze([void 0], t, !1)))();
}, {
  strategy: Be.variadic
});
var ko = function(e) {
  process.env.NODE_ENV !== "production" && console.error(e);
}, Lo = function(e) {
  process.env.NODE_ENV !== "production" && console.warn(e);
}, Ho = {
  formats: {},
  messages: {},
  timeZone: void 0,
  defaultLocale: "en",
  defaultFormats: {},
  fallbackOnEmptyString: !0,
  onError: ko,
  onWarn: Lo
};
function Fo(e) {
  Io(e, "[React Intl] Could not find required `intl` object. <IntlProvider> needs to exist in the component ancestry.");
}
et(et({}, Ho), { textComponent: $.Fragment });
function dr(e, t) {
  if (e === t)
    return !0;
  if (!e || !t)
    return !1;
  var r = Object.keys(e), n = Object.keys(t), o = r.length;
  if (n.length !== o)
    return !1;
  for (var i = 0; i < o; i++) {
    var s = r[i];
    if (e[s] !== t[s] || !Object.prototype.hasOwnProperty.call(t, s))
      return !1;
  }
  return !0;
}
var Ct = { exports: {} }, K = {};
/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var hr;
function Vo() {
  if (hr) return K;
  hr = 1;
  var e = typeof Symbol == "function" && Symbol.for, t = e ? Symbol.for("react.element") : 60103, r = e ? Symbol.for("react.portal") : 60106, n = e ? Symbol.for("react.fragment") : 60107, o = e ? Symbol.for("react.strict_mode") : 60108, i = e ? Symbol.for("react.profiler") : 60114, s = e ? Symbol.for("react.provider") : 60109, f = e ? Symbol.for("react.context") : 60110, c = e ? Symbol.for("react.async_mode") : 60111, l = e ? Symbol.for("react.concurrent_mode") : 60111, g = e ? Symbol.for("react.forward_ref") : 60112, d = e ? Symbol.for("react.suspense") : 60113, I = e ? Symbol.for("react.suspense_list") : 60120, k = e ? Symbol.for("react.memo") : 60115, T = e ? Symbol.for("react.lazy") : 60116, x = e ? Symbol.for("react.block") : 60121, N = e ? Symbol.for("react.fundamental") : 60117, E = e ? Symbol.for("react.responder") : 60118, C = e ? Symbol.for("react.scope") : 60119;
  function b(u) {
    if (typeof u == "object" && u !== null) {
      var H = u.$$typeof;
      switch (H) {
        case t:
          switch (u = u.type, u) {
            case c:
            case l:
            case n:
            case i:
            case o:
            case d:
              return u;
            default:
              switch (u = u && u.$$typeof, u) {
                case f:
                case g:
                case T:
                case k:
                case s:
                  return u;
                default:
                  return H;
              }
          }
        case r:
          return H;
      }
    }
  }
  function M(u) {
    return b(u) === l;
  }
  return K.AsyncMode = c, K.ConcurrentMode = l, K.ContextConsumer = f, K.ContextProvider = s, K.Element = t, K.ForwardRef = g, K.Fragment = n, K.Lazy = T, K.Memo = k, K.Portal = r, K.Profiler = i, K.StrictMode = o, K.Suspense = d, K.isAsyncMode = function(u) {
    return M(u) || b(u) === c;
  }, K.isConcurrentMode = M, K.isContextConsumer = function(u) {
    return b(u) === f;
  }, K.isContextProvider = function(u) {
    return b(u) === s;
  }, K.isElement = function(u) {
    return typeof u == "object" && u !== null && u.$$typeof === t;
  }, K.isForwardRef = function(u) {
    return b(u) === g;
  }, K.isFragment = function(u) {
    return b(u) === n;
  }, K.isLazy = function(u) {
    return b(u) === T;
  }, K.isMemo = function(u) {
    return b(u) === k;
  }, K.isPortal = function(u) {
    return b(u) === r;
  }, K.isProfiler = function(u) {
    return b(u) === i;
  }, K.isStrictMode = function(u) {
    return b(u) === o;
  }, K.isSuspense = function(u) {
    return b(u) === d;
  }, K.isValidElementType = function(u) {
    return typeof u == "string" || typeof u == "function" || u === n || u === l || u === i || u === o || u === d || u === I || typeof u == "object" && u !== null && (u.$$typeof === T || u.$$typeof === k || u.$$typeof === s || u.$$typeof === f || u.$$typeof === g || u.$$typeof === N || u.$$typeof === E || u.$$typeof === C || u.$$typeof === x);
  }, K.typeOf = b, K;
}
var G = {};
/** @license React v16.13.1
 * react-is.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var mr;
function zo() {
  return mr || (mr = 1, process.env.NODE_ENV !== "production" && function() {
    var e = typeof Symbol == "function" && Symbol.for, t = e ? Symbol.for("react.element") : 60103, r = e ? Symbol.for("react.portal") : 60106, n = e ? Symbol.for("react.fragment") : 60107, o = e ? Symbol.for("react.strict_mode") : 60108, i = e ? Symbol.for("react.profiler") : 60114, s = e ? Symbol.for("react.provider") : 60109, f = e ? Symbol.for("react.context") : 60110, c = e ? Symbol.for("react.async_mode") : 60111, l = e ? Symbol.for("react.concurrent_mode") : 60111, g = e ? Symbol.for("react.forward_ref") : 60112, d = e ? Symbol.for("react.suspense") : 60113, I = e ? Symbol.for("react.suspense_list") : 60120, k = e ? Symbol.for("react.memo") : 60115, T = e ? Symbol.for("react.lazy") : 60116, x = e ? Symbol.for("react.block") : 60121, N = e ? Symbol.for("react.fundamental") : 60117, E = e ? Symbol.for("react.responder") : 60118, C = e ? Symbol.for("react.scope") : 60119;
    function b(S) {
      return typeof S == "string" || typeof S == "function" || // Note: its typeof might be other than 'symbol' or 'number' if it's a polyfill.
      S === n || S === l || S === i || S === o || S === d || S === I || typeof S == "object" && S !== null && (S.$$typeof === T || S.$$typeof === k || S.$$typeof === s || S.$$typeof === f || S.$$typeof === g || S.$$typeof === N || S.$$typeof === E || S.$$typeof === C || S.$$typeof === x);
    }
    function M(S) {
      if (typeof S == "object" && S !== null) {
        var ye = S.$$typeof;
        switch (ye) {
          case t:
            var Re = S.type;
            switch (Re) {
              case c:
              case l:
              case n:
              case i:
              case o:
              case d:
                return Re;
              default:
                var Oe = Re && Re.$$typeof;
                switch (Oe) {
                  case f:
                  case g:
                  case T:
                  case k:
                  case s:
                    return Oe;
                  default:
                    return ye;
                }
            }
          case r:
            return ye;
        }
      }
    }
    var u = c, H = l, V = f, J = s, R = t, P = g, L = n, B = T, Y = k, m = r, v = i, p = o, w = d, y = !1;
    function A(S) {
      return y || (y = !0, console.warn("The ReactIs.isAsyncMode() alias has been deprecated, and will be removed in React 17+. Update your code to use ReactIs.isConcurrentMode() instead. It has the exact same API.")), D(S) || M(S) === c;
    }
    function D(S) {
      return M(S) === l;
    }
    function U(S) {
      return M(S) === f;
    }
    function Z(S) {
      return M(S) === s;
    }
    function Q(S) {
      return typeof S == "object" && S !== null && S.$$typeof === t;
    }
    function ae(S) {
      return M(S) === g;
    }
    function se(S) {
      return M(S) === n;
    }
    function me(S) {
      return M(S) === T;
    }
    function fe(S) {
      return M(S) === k;
    }
    function ve(S) {
      return M(S) === r;
    }
    function de(S) {
      return M(S) === i;
    }
    function re(S) {
      return M(S) === o;
    }
    function pe(S) {
      return M(S) === d;
    }
    G.AsyncMode = u, G.ConcurrentMode = H, G.ContextConsumer = V, G.ContextProvider = J, G.Element = R, G.ForwardRef = P, G.Fragment = L, G.Lazy = B, G.Memo = Y, G.Portal = m, G.Profiler = v, G.StrictMode = p, G.Suspense = w, G.isAsyncMode = A, G.isConcurrentMode = D, G.isContextConsumer = U, G.isContextProvider = Z, G.isElement = Q, G.isForwardRef = ae, G.isFragment = se, G.isLazy = me, G.isMemo = fe, G.isPortal = ve, G.isProfiler = de, G.isStrictMode = re, G.isSuspense = pe, G.isValidElementType = b, G.typeOf = M;
  }()), G;
}
process.env.NODE_ENV === "production" ? Ct.exports = Vo() : Ct.exports = zo();
var Yo = Ct.exports, Zr = Yo, Bo = {
  $$typeof: !0,
  render: !0,
  defaultProps: !0,
  displayName: !0,
  propTypes: !0
}, Uo = {
  $$typeof: !0,
  compare: !0,
  defaultProps: !0,
  displayName: !0,
  propTypes: !0,
  type: !0
}, Qr = {};
Qr[Zr.ForwardRef] = Bo;
Qr[Zr.Memo] = Uo;
var $t = typeof window < "u" && !window.__REACT_INTL_BYPASS_GLOBAL_CONTEXT__ ? window.__REACT_INTL_CONTEXT__ || (window.__REACT_INTL_CONTEXT__ = $.createContext(null)) : $.createContext(null);
$t.Consumer;
$t.Provider;
var Wo = $t;
function at() {
  var e = $.useContext(Wo);
  return Fo(e), e;
}
var _t;
(function(e) {
  e.formatDate = "FormattedDate", e.formatTime = "FormattedTime", e.formatNumber = "FormattedNumber", e.formatList = "FormattedList", e.formatDisplayName = "FormattedDisplayName";
})(_t || (_t = {}));
var Ot;
(function(e) {
  e.formatDate = "FormattedDateParts", e.formatTime = "FormattedTimeParts", e.formatNumber = "FormattedNumberParts", e.formatList = "FormattedListParts";
})(Ot || (Ot = {}));
function en(e) {
  var t = function(r) {
    var n = at(), o = r.value, i = r.children, s = tt(r, ["value", "children"]), f = typeof o == "string" ? new Date(o || 0) : o, c = e === "formatDate" ? n.formatDateToParts(f, s) : n.formatTimeToParts(f, s);
    return i(c);
  };
  return t.displayName = Ot[e], t;
}
function Ue(e) {
  var t = function(r) {
    var n = at(), o = r.value, i = r.children, s = tt(
      r,
      ["value", "children"]
    ), f = n[e](o, s);
    if (typeof i == "function")
      return i(f);
    var c = n.textComponent || $.Fragment;
    return $.createElement(c, null, f);
  };
  return t.displayName = _t[e], t;
}
function Ko(e, t) {
  var r = e.values, n = tt(e, ["values"]), o = t.values, i = tt(t, ["values"]);
  return dr(o, r) && dr(n, i);
}
function tn(e) {
  var t = at(), r = t.formatMessage, n = t.textComponent, o = n === void 0 ? $.Fragment : n, i = e.id, s = e.description, f = e.defaultMessage, c = e.values, l = e.children, g = e.tagName, d = g === void 0 ? o : g, I = e.ignoreTag, k = { id: i, description: s, defaultMessage: f }, T = r(k, c, {
    ignoreTag: I
  });
  return typeof l == "function" ? l(Array.isArray(T) ? T : [T]) : d ? $.createElement(d, null, $.Children.toArray(T)) : $.createElement($.Fragment, null, T);
}
tn.displayName = "FormattedMessage";
var Pt = $.memo(tn, Ko);
Pt.displayName = "MemoizedFormattedMessage";
Ue("formatDate");
Ue("formatTime");
Ue("formatNumber");
Ue("formatList");
Ue("formatDisplayName");
en("formatDate");
en("formatTime");
const Go = (e, t = 2) => {
  if (isNaN(e))
    return 0;
  let r = Math.floor(e).toString(), n = Math.round(e % 1 * 100).toString().padEnd(t, "0"), o = [];
  for (; r.length > 3; )
    o.unshift(r.slice(-3)), r = r.slice(0, -3);
  o.unshift(r);
  let i = o.join(",");
  return n ? `${i}.${n}` : i;
}, vr = localStorage.getItem(rt.THEME) || "";
function rn(e = 1) {
  let t = 100;
  switch (e) {
    case 1:
      t = 100;
      break;
    case 2:
      t = 116;
      break;
    case 3:
      t = 136;
      break;
    case 4:
      t = 168;
      break;
    case 5:
      t = 232;
      break;
    case 6:
      t = 280;
      break;
  }
  return t;
}
const we = (e, t) => {
  const r = e ? `${e}.${t}` : t;
  return /* @__PURE__ */ _.jsx(Pt, { id: r });
}, pr = (e, t, r = "", n = !0) => {
  const o = he.get(he.find(t, { key: e }), "value", "");
  return o ? n ? /* @__PURE__ */ _.jsxs(_.Fragment, { children: [
    e && /* @__PURE__ */ _.jsxs("span", { children: [
      e,
      " - "
    ] }),
    we(r, o)
  ] }) : we(r, o) : e;
}, nn = (e, t) => {
  const { valueType: r, dictType: n, data: o, prefix: i, showKey: s, amountLength: f, optionPrefix: c } = t;
  switch (r) {
    case Ne.Amount:
      return Go(e, f);
    case Ne.Dictionary:
      return pr(e, [], i, s);
    case Ne.MockDictionary:
      return pr(e, o, c || i, s);
    case Ne.DateTime:
      return e && fr(e).format(ra);
    case Ne.Date:
      return e && fr(e).format(ta);
    default:
      return e;
  }
}, qo = (e) => {
  let { valueType: t, render: r } = e;
  const n = { ...e };
  switch (t) {
    case Ne.Ellipsis:
      n.ellipsis = !0;
      break;
    default:
      he.isNil(r) && (n.render = (o) => nn(o, e));
      break;
  }
  return delete n.data, n;
}, gr = {
  copy: /* @__PURE__ */ _.jsx(kr, {}),
  detail: /* @__PURE__ */ _.jsx(Fr, {}),
  edit: /* @__PURE__ */ _.jsx(St, {}),
  delete: /* @__PURE__ */ _.jsx(Lr, {}),
  cancel: /* @__PURE__ */ _.jsx(jr, {}),
  save: /* @__PURE__ */ _.jsx(Wr, {}),
  up: /* @__PURE__ */ _.jsx(Ar, {}),
  down: /* @__PURE__ */ _.jsx(Dr, {}),
  search: /* @__PURE__ */ _.jsx(Rt, {}),
  flowNode: /* @__PURE__ */ _.jsx(Nr, {}),
  dispatch: /* @__PURE__ */ _.jsx(Kr, {}),
  dowload: /* @__PURE__ */ _.jsx(Hr, {}),
  aiHelp: /* @__PURE__ */ _.jsx(Ir, {}),
  menuAuth: /* @__PURE__ */ _.jsx(Yr, {}),
  userDetail: /* @__PURE__ */ _.jsx(zr, {}),
  fieldManage: /* @__PURE__ */ _.jsx(Vr, {})
}, _e = (e) => {
  const { type: t, disabled: r = !1, loading: n = !1, onClick: o = () => {
  } } = e, i = we(Ce.COMMON, t);
  switch (t) {
    case q.copy:
    case q.detail:
    case q.edit:
    case q.cancel:
    case q.save:
    case q.up:
    case q.down:
    case q.flowNode:
    case q.dispatch:
    case q.dowload:
    case q.aiHelp:
    case q.menuAuth:
    case q.userDetail:
    case q.fieldManage:
      return /* @__PURE__ */ _.jsx(Je, { placement: "top", title: i, children: /* @__PURE__ */ _.jsx(
        Ee,
        {
          type: "link",
          style: { color: vr },
          icon: gr[t],
          disabled: r,
          loading: n,
          onClick: () => o(t)
        }
      ) }, t);
    case q.delete:
      return /* @__PURE__ */ _.jsx(Je, { placement: "top", title: i, children: /* @__PURE__ */ _.jsx(
        In,
        {
          title: we(Ce.COMMON, "confirmDelete"),
          okText: we(Ce.COMMON, "confirm"),
          cancelText: we(Ce.COMMON, "cancel"),
          onConfirm: () => o(t),
          children: /* @__PURE__ */ _.jsx(Ee, { type: "link", icon: gr[t], disabled: r, danger: !0, loading: n })
        }
      ) }, t);
    default:
      return /* @__PURE__ */ _.jsx(Je, { title: we(Ce.COMMON, "edit"), children: /* @__PURE__ */ _.jsx(
        Ee,
        {
          type: "link",
          style: { color: vr },
          icon: /* @__PURE__ */ _.jsx(St, {}),
          disabled: r,
          loading: n,
          onClick: () => o(t)
        }
      ) });
  }
}, Jo = (e) => {
  switch (localStorage.getItem("locale")) {
    case na.EN.locale:
      return `Toal ${e}`;
    default:
      return `共 ${e} 条`;
  }
}, Xo = () => {
  const e = {
    permissionButtons: []
  };
  return {
    hasPermission: (r) => {
      for (const n of e.permissionButtons)
        if (r === n.permissionCode)
          return !0;
      return !0;
    }
  };
}, Zo = localStorage.getItem(rt.THEME) || "", We = () => {
  const e = at(), { hasPermission: t } = Xo(), r = (T, x, N) => {
    const E = `${T}.${x}`;
    let C = e.formatMessage({ id: E }) || "";
    return N && Object.entries(N).forEach(([b, M]) => {
      C = C.replace(`{${b}}`, String(M));
    }), C;
  }, n = (T, x, N, E) => {
    Ln[x]({
      message: r("common", "tip"),
      duration: E || 3,
      description: r(T, N)
    });
  }, o = (T, x, N = "") => {
    const E = q[T] && T !== q.list ? r("common", q[T]) : "", C = N ? r(x, N) : "";
    return `${E}${C}`;
  }, i = (T, x, N, E) => {
    let C = E ? r("common", "success") : r("common", "fail");
    return o(N, x, T) + C;
  }, s = (T, x) => {
    const N = T ? `${T}.${x}` : x;
    return /* @__PURE__ */ _.jsx(Pt, { id: N });
  }, f = (T, x) => e.formatDate(T, x), c = (T, x) => e.formatNumber(T, x), l = (T, x = !0, N = "") => {
    const E = (C) => C.map((b) => {
      var H;
      const M = N ? r(N, b.value) : b.value, u = {
        ...b,
        key: b.key,
        value: b.key,
        label: x && ((H = b.key) != null && H.trim()) ? `${b.key} - ${M}` : M
      };
      return b.children && b.children.length > 0 && (u.children = E(b.children)), u;
    });
    return T && T.length > 0 ? E(T) : [];
  }, g = (T, x = !0, N = "") => {
    const E = {};
    return T.forEach((C) => {
      const b = N ? r(N, C.value) : C.value;
      E[C.key] = {
        text: x && C.key ? `${C.key} - ${b}` : b
      };
    }), E;
  }, d = (T = [], x = (N) => {
  }) => {
    if (T && T.length > 0) {
      const N = [];
      return T.forEach((E) => {
        let C, b, M = !1, u, H;
        typeof E == "object" ? (C = E.type, b = E.title || E.type, b = E.prefix && b ? r(E.prefix, b) : b, M = (E == null ? void 0 : E.disabled) || !1, u = E == null ? void 0 : E.icon, H = E.permissionId) : typeof E == "string" && (C = E), !(H && !t(H)) && (u ? N.push(
          /* @__PURE__ */ _.jsx(Je, { placement: "top", title: b, children: /* @__PURE__ */ _.jsx(
            Ee,
            {
              type: "link",
              icon: u,
              disabled: M ?? !1,
              style: { color: Zo },
              onClick: () => x(E)
            }
          ) }, C)
        ) : C && N.push(_e({ type: C, disabled: M, onClick: x })));
      }), /* @__PURE__ */ _.jsx(kn, { children: N });
    } else
      return null;
  }, I = r("common", "inputPlaceholder"), k = r("common", "selectPlaceholder");
  return {
    translate: r,
    formatActionTitle: o,
    formateHtmlText: s,
    formatLocalDate: f,
    formatLocalNumber: c,
    getSelectOption: l,
    getEditTableSelectOption: g,
    getActionResultTitle: i,
    openNotificationTip: n,
    defaultInputPlaceholder: I,
    defaultSelectPlaceholder: k,
    renderActionColumn: d
  };
}, mi = ({ descriptionsItems: e, bordered: t, size: r, prefix: n, num: o }) => {
  const { translate: i } = We(), s = () => {
    let f = [];
    for (let c in e) {
      const l = typeof e[c];
      if (e[c] && l === "object") {
        const d = e[c];
        let I = "", k = he.isNil(d == null ? void 0 : d.prefixKey) ? n : d.prefixKey;
        he.isNil(d == null ? void 0 : d.context) || (I = nn(d.context, {
          ...d,
          prefix: k
        }));
        const T = k ? i(k, c) : c, x = he.isNil(d.props) ? {} : { ...d.props };
        f.push({
          key: c,
          label: T,
          children: I,
          ...x
        });
      } else
        f.push({
          key: c,
          label: i(n, c),
          children: e[c]
        });
    }
    return f;
  };
  return /* @__PURE__ */ _.jsx(
    Hn,
    {
      bordered: t,
      size: r,
      column: o || 3,
      items: s(),
      style: { width: "380px" }
    }
  );
}, mt = ".5rem", Pe = {
  mls: { marginLeft: mt },
  mtbs: { marginTop: mt, marginBottom: mt }
}, an = {
  green: "#00994e",
  blue: "#345da7"
  // red: '#f5222d',
  // dark: '#282a36',
}, Qo = localStorage.getItem(rt.THEME) || "", ei = ({
  children: e,
  color: t = Qo || an.green,
  type: r,
  size: n,
  icon: o,
  style: i,
  disabled: s = !1,
  loading: f = !1,
  onClick: c = () => {
  }
}) => /* @__PURE__ */ _.jsx(
  Fn,
  {
    theme: {
      components: {
        Button: {
          colorPrimary: t
        }
      }
    },
    children: /* @__PURE__ */ _.jsx(
      Ee,
      {
        type: r || "primary",
        size: n || "middle",
        disabled: s,
        loading: f,
        icon: o,
        style: i,
        onClick: he.debounce(c),
        children: e
      }
    )
  }
), He = 160, vt = { marginRight: "2rem" }, ti = localStorage.getItem(rt.THEME) || an.blue, vi = ({
  children: e,
  searchTitle: t = "",
  resetTitle: r = "",
  createTitle: n = "",
  searchValue: o = {},
  resetValue: i = {},
  searchSource: s = [],
  intlPrefix: f = "",
  buttonDisabled: c = !1,
  onSearch: l = (k) => {
  },
  onCreate: g = () => {
  },
  onChange: d,
  onRest: I
}) => {
  const { translate: k, getSelectOption: T, defaultInputPlaceholder: x, defaultSelectPlaceholder: N } = We(), [E, C] = Ve(o), [b] = Xe.useForm(), M = (R) => {
    const P = { rules: R.rules, name: R.value, label: void 0 };
    if (R.label) {
      const L = R.prefix || f;
      P.label = L ? k(L, R.label) : R.label;
    }
    return P;
  }, u = (R) => {
    const P = { allowClear: !0, style: {} }, L = Object.values(ia);
    for (const v in R)
      L.includes(v) || (P[v] = R[v]);
    const { type: B, width: Y } = R;
    let m = null;
    switch (B) {
      case xe.INPUT:
        return P.style = { width: Y || He }, m = /* @__PURE__ */ _.jsx(Gt, { placeholder: x, ...P }), m;
      case xe.CHECK_BOX:
        return m = /* @__PURE__ */ _.jsx(Bn.Group, { options: R.data }), m;
      case xe.SELECT: {
        P.style = { width: Y || He };
        const v = R.prefix || f;
        let p = !(R.isint && R.isint === "0"), w = [];
        return he.isNil(R.dictType) ? w = R.data && R.bind ? R.data.filter((y) => y.bind === E[R.bind] || y.key === "") : R.data : p = !1, m = /* @__PURE__ */ _.jsx(
          Yn,
          {
            placeholder: N,
            ...P,
            options: p ? T(w, R.showKey, v) : w
          }
        ), m;
      }
      case xe.TREE_SELECT:
        return P.style = { width: Y || He }, m = /* @__PURE__ */ _.jsx(zn, { ...P, treeData: R.data }), m;
      case xe.CASCADER:
        return P.style = { width: Y || He }, m = /* @__PURE__ */ _.jsx(Vn, { placeholder: N, ...P, options: R.data }), m;
      case xe.DATE_PICKER:
        return P.style = { width: Y || He }, m = /* @__PURE__ */ _.jsx(qt, { ...P }), m;
      case xe.RANGE_PICKER:
        return P.style = { width: Y || 240 }, m = /* @__PURE__ */ _.jsx(qt.RangePicker, { ...P }), m;
      default:
        return /* @__PURE__ */ _.jsx(Gt, { ...P });
    }
  }, H = (R, P) => {
    d && d(R, P);
    let L = Object.keys(R)[0], B = "";
    s == null || s.forEach((Y) => {
      Y.value === L && Y.ref && (B = Y.ref);
    }), B ? (C({ ...P, [B]: null }), b.setFieldsValue({ [B]: null })) : C(P);
  }, V = () => {
    b.setFieldsValue(i), I && I(o), J();
  }, J = () => {
    b.validateFields().then((R) => {
      l(R);
    }).catch((R) => {
      R.errorFields && b.setFields(R.errorFields);
    });
  };
  return /* @__PURE__ */ _.jsx("div", { className: "search-box", children: /* @__PURE__ */ _.jsxs(
    Xe,
    {
      layout: "inline",
      colon: !1,
      form: b,
      initialValues: o,
      onValuesChange: he.debounce(H),
      children: [
        s == null ? void 0 : s.map((R) => /* @__PURE__ */ _.jsx(Xe.Item, { ...M(R), style: { ...Pe.mtbs, ...vt }, children: u(R) }, R.value)),
        /* @__PURE__ */ _.jsx(
          Ee,
          {
            type: "primary",
            icon: /* @__PURE__ */ _.jsx(Rt, {}),
            disabled: c,
            style: Pe.mtbs,
            onClick: he.debounce(() => J()),
            children: t
          }
        ),
        /* @__PURE__ */ _.jsx(
          Ee,
          {
            icon: /* @__PURE__ */ _.jsx(Ur, {}),
            disabled: c,
            style: { ...Pe.mtbs, ...Pe.mls, ...vt },
            onClick: he.debounce(V),
            children: r
          }
        ),
        n && /* @__PURE__ */ _.jsx(
          ei,
          {
            color: ti,
            type: "primary",
            size: "middle",
            icon: /* @__PURE__ */ _.jsx(Br, {}),
            disabled: c,
            style: { ...Pe.mtbs, ...vt },
            onClick: g,
            children: n
          }
        ),
        e && /* @__PURE__ */ _.jsx("div", { style: Pe.mtbs, children: e })
      ]
    }
  ) });
}, ri = "_commonParamTable_1ilux_10", ni = {
  commonParamTable: ri
}, ai = { currentPage: 1, defaultPageSize: 50, pageSize: 50 }, oi = (e, t, r, n) => e.length ? [
  {
    title: t(Ce.COMMON, "option"),
    dataIndex: "option",
    key: "option",
    align: "center",
    fixed: "right",
    width: rn(e.length - 2),
    render: (o, i) => r(e, (s) => n(s, i, e))
  }
] : [], pi = ({
  rowKey: e = "id",
  columns: t = [],
  optionList: r = [],
  paginationConfig: n,
  dataSource: o = [],
  loading: i = !1,
  intlPrefix: s,
  components: f,
  props: c = {},
  onAction: l,
  onChange: g
}) => {
  const { translate: d, renderActionColumn: I } = We(), [k, T] = Ve([]), x = ut(l || (() => {
  }), [l]), N = ut(g || (() => {
  }), [g]);
  Qe(() => {
    Array.isArray(o) ? T(o) : T([]);
  }, [o]);
  const E = ut(
    (M) => M.map((u) => {
      u = { width: 120, align: "center", ellipsis: !0, ...u };
      let { title: H, key: V, prefix: J, children: R } = u;
      u.prefix = J || s, H = d(u.prefix, H);
      const P = qo(u);
      return delete P.valueType, Array.isArray(R) && (P.children = E(R)), { ...P, title: H };
    }),
    [s, d]
  ), C = Wt(() => {
    const M = E(t), u = oi(r, d, I, x);
    return [...M, ...u];
  }, [t, r, d, I, x, E]), b = Wt(() => typeof n == "object" ? { ...ai, ...n, showTotal: Jo } : n, [n]);
  return /* @__PURE__ */ _.jsx(_.Fragment, { children: /* @__PURE__ */ _.jsx(
    Un,
    {
      className: ni.commonParamTable,
      tableLayout: "fixed",
      rowKey: e,
      components: f,
      dataSource: k,
      columns: C,
      loading: i,
      pagination: b,
      scroll: {
        x: "max-content",
        y: 2e3
      },
      onChange: N,
      ...c
    }
  ) });
}, ii = ({
  intlPrefix: e,
  rowKey: t,
  optionCount: r,
  orderFiled: n,
  editableKeys: o,
  tableData: i,
  setTableData: s,
  getOptionList: f,
  onAction: c
}) => {
  const { translate: l, getEditTableSelectOption: g, openNotificationTip: d } = We(), I = (x, N, E) => f(x).map((b) => {
    const { optionType: M, disabled: u = !1, loading: H = !1 } = b, V = { type: M, disabled: u, loading: H };
    switch (M) {
      case q.edit:
        return _e({
          ...V,
          onClick: () => {
            var J;
            (J = E == null ? void 0 : E.startEditable) == null || J.call(E, x[t]), c(M, x);
          }
        });
      case q.detail:
        return _e({
          ...V,
          onClick: () => c(M, x)
        });
      case q.delete:
        return _e({
          ...V,
          onClick: () => {
            s((J) => J.filter((R) => R[t] !== x[t])), c(M, x);
          }
        });
      case q.copy:
        return _e({
          ...V,
          onClick: () => {
            var R;
            const J = JSON.parse(JSON.stringify(i));
            J.push({ ...x, [`${t}`]: Date.now(), isAdd: !0 }), s(J), (R = E == null ? void 0 : E.startEditable) == null || R.call(E, J.at(-1)[t]), c(M, x);
          }
        });
      case q.up:
      case q.down:
        return _e({
          ...V,
          onClick: () => {
            T(N, M), c(M, x);
          }
        });
      default:
        return /* @__PURE__ */ _.jsx(_.Fragment, {});
    }
  }), k = (x) => {
    const N = x.map((E) => {
      const C = { ...E }, { prefix: b = e, title: M, valueType: u, data: H, showKey: V = !1, optionPrefix: J } = E, R = l(b, M);
      let P = {};
      return u === aa.SELECT && !he.isNil(H) && (P = { valueEnum: g(H, V, he.isNil(J) ? b : J) }, delete C.data, delete C.showKey), { ...C, ...P, title: R };
    });
    return r && N.push({
      title: l(Ce.COMMON, "option"),
      valueType: "option",
      align: "center",
      width: rn(r),
      render: (E, C, b, M) => I(C, b, M)
    }), N;
  }, T = (x, N) => {
    if (o.length) {
      d("common", dt.WARNING, "formEditing");
      return;
    }
    if (N === q.up && x === 0) {
      d("common", dt.WARNING, "firstRow");
      return;
    }
    if (N === q.down && x === i.length - 1) {
      d("common", dt.WARNING, "lastRow");
      return;
    }
    let E = N === q.up ? x - 1 : x + 1;
    s((C) => {
      const b = { ...C[x] }, M = { ...C[E] }, u = n ? { [n]: M == null ? void 0 : M[n] } : {}, H = n ? { [n]: b == null ? void 0 : b[n] } : {}, V = [...C], [J, R] = [V[x], V[E]];
      return V[x] = { ...R, ...H }, V[E] = { ...J, ...u }, V;
    });
  };
  return {
    getColumns: k
  };
}, gi = Pn(
  ({
    rowKey: e,
    columns: t,
    dataSource: r,
    intlPrefix: n,
    canEdit: o,
    editableType: i = "single",
    showCreate: s = !0,
    optionCount: f = 0,
    loading: c = !1,
    orderFiled: l,
    editTableType: g,
    getOptionList: d = () => [],
    afterMount: I = () => {
    },
    onCreate: k = (C) => ({ id: C + 1 }),
    onAction: T = (C, b) => {
    },
    onFormChange: x = (C, b, M) => {
    },
    onFormSave: N = (C, b, M) => {
    }
  }, E = null) => {
    const { translate: C } = We(), [b, M] = Ve([]), [u, H] = Ve([]), V = Kt(), J = Kt(null), [R] = Xe.useForm(), { getColumns: P } = ii({
      intlPrefix: n,
      rowKey: e,
      optionCount: f,
      orderFiled: l,
      editableKeys: b,
      tableData: u,
      setTableData: H,
      getOptionList: d,
      onAction: T
    });
    Nn(E, () => ({
      // 校验表单
      async validateFields() {
        var B;
        try {
          return i === "single" && b.length ? { errorCode: oa.EDITING } : (await ((B = V == null ? void 0 : V.current) == null ? void 0 : B.validateFields()), u);
        } catch {
          return null;
        }
      },
      // 重置
      resetFields() {
        Array.isArray(b) && (M([]), R.resetFields()), H(r);
      },
      // 获取当前表格数据
      getFieldsValue() {
        var B;
        return (B = V == null ? void 0 : V.current) == null ? void 0 : B.getFieldsValue();
      },
      // 手动设置行数据
      setRowData(B, Y) {
        var m, v;
        (v = (m = V.current) == null ? void 0 : m.setRowData) == null || v.call(m, B, { ...Y });
      },
      // 获取行数据
      getRowData(B) {
        var Y, m;
        return ((Y = V.current) == null ? void 0 : Y.getRowData) && ((m = V.current) == null ? void 0 : m.getRowData(B));
      },
      // 手动设置可编辑的行
      setCustomEditableKeys(B) {
        M(B);
      },
      // 获取正在编辑的行
      getEditableKeys() {
        return b;
      }
    })), Qe(() => {
      H(Array.isArray(r) ? r : []);
    }, [r]), Qe(() => {
      I(R);
    }, []);
    const L = {
      rowKey: e,
      loading: c,
      // 列表配置
      columns: P(t),
      // 数据源
      value: u,
      // 新增按钮配置
      recordCreatorProps: o && s ? {
        position: "bottom",
        // 每次新增的时候需要rowKey
        record: k,
        creatorButtonText: C(Ce.COMMON, "create")
        // style: createButtonStyle,
      } : !1,
      // 可编辑表格实例
      editableFormRef: V,
      // action的实例
      actionRef: J,
      // 可编辑表格配置
      editable: {
        form: R,
        // 单行编辑还是多行编辑
        type: i,
        // 编辑中的行keys
        editableKeys: b,
        onChange: M,
        onSave: (B, Y, m) => N(B, Y, m),
        // 默认操作列的配置
        actionRender: (B, Y, m) => [m.save, m.cancel],
        saveText: _e({ type: q.save }),
        cancelText: _e({ type: q.cancel })
      },
      onChange: H
    };
    return g === "caseDemo" && delete L.onChange, /* @__PURE__ */ _.jsx(qn, { ...L });
  }
), yi = ({ action: e, maxSize: t = 10, onSuccess: r, onError: n }) => {
  const [o, i] = Ve(!1), s = (l) => l.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" || l.type === "application/vnd.ms-excel" || /\.xlsx?$/i.test(l.name) ? l.size / 1024 / 1024 <= t ? !0 : (Ge.error(`文件大小不能超过 ${t}MB`), ft.LIST_IGNORE) : (Ge.error("只能上传 Excel 文件 (.xlsx, .xls)"), ft.LIST_IGNORE), f = (l, g) => {
    var I;
    const d = ((I = l == null ? void 0 : l.response) == null ? void 0 : I.message) || (l == null ? void 0 : l.message) || "上传失败";
    Ge.error(`${g} ${d}`), n == null || n(new Error(d));
  }, c = {
    name: "file",
    action: e,
    accept: ".xlsx, .xls",
    // 文件选择器过滤
    multiple: !1,
    // 单文件上传
    showUploadList: !1,
    // 不显示文件列表
    beforeUpload: s,
    headers: {
      // 添加正确的请求头
      "Content-Type": "multipart/form-data"
    },
    onChange(l) {
      var g, d;
      if (l.file.status === "uploading") {
        i(!0);
        return;
      }
      if (l.file.status === "done") {
        if (((d = (g = l.file.response) == null ? void 0 : g.header) == null ? void 0 : d.errorCode) !== "000000") {
          f(l.file.response, l.file.name), i(!1);
          return;
        }
        Ge.success(`${l.file.name} 上传成功`), i(!1), r == null || r(l.file.originFileObj);
      } else l.file.status === "error" && (f(l.file.response, l.file.name), i(!1));
    }
  };
  return /* @__PURE__ */ _.jsx(ft, { ...c, children: /* @__PURE__ */ _.jsx(Ee, { style: { marginRight: 6 }, icon: /* @__PURE__ */ _.jsx(Gr, {}), type: "primary", loading: o, children: "下载模板" }) });
};
export {
  fi as Button,
  di as Card,
  hi as CommonModal,
  pi as CommonTable,
  gi as EditTable,
  ei as GradientButton,
  vi as Search,
  mi as TabDescription,
  yi as UploadCom
};
