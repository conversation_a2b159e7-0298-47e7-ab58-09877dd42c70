import { ReactNode } from 'react';

/**
 * 菜单子项
 */
export type TMenuItem = {
    code: string;
    parentCode?: string;
    name?: string;
    type?: string;
    url?: string;
    level: number;
    sort: number;
    children?: TMenuItem[];
};
export type TSysMenuItem = {
    key?: string | number;
    permissionId?: string;
    resourceId?: string;
    id: string;
    permissionCode?: string;
    resourceName: string;
    resourceUrl: string;
    parentId?: string | number;
    displayOrder: string | number;
    identifierField: string;
    resourceDesc: string;
    createTime: string;
    updateTime: string;
    createUser: string;
    updateUser: string;
    permissions?: Array<any>;
    children?: TSysMenuItem[];
};
/**
 * 下拉框、复选框子项
 */
export type TOptionItem = {
    key: string;
    value: string;
    label?: string | ReactNode;
    children?: [];
};
/**
 * 通知类型
 */
export type TNotification = 'success' | 'info' | 'warning' | 'error';
/**
 * 下拉菜单子项
 */
export type TDropDownMenuItem = {
    key: string;
    label: string | ReactNode;
    icon?: ReactNode | any;
    disabled?: boolean;
};
