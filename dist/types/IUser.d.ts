/**
 * Created by ca<PERSON><PERSON> on 2024/7/1.
 */
export interface ILoginParams {
    username: string;
}
export interface ILoginResult {
    success?: boolean;
    userType?: 'user' | 'admin' | 'guest';
}
export interface IUserInfo {
    id: number;
    userId: string;
    userCode: string;
    userName: string;
    empName: string;
    branchName: string;
    userRole?: Array<string>;
    avatar: string;
    organizationId: string;
    organizationName: string;
    createTime: string;
    updateTime: string;
    userType: 'admin' | 'user';
}
