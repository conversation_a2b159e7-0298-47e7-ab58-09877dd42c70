import { IPaniation } from './ICommon';

/**
 * 请求接口传参
 */
export interface IQueryParam {
    /**
     * 接口
     */
    url: string;
    param?: object;
    /**
     * 查询条件
     */
    searchValue?: object;
    /**
     * 分页数据
     */
    pagination?: IPaniation;
    /**
     * 去重字段
     */
    groupByFieldList?: Array<''>;
    /**
     * 排序字段
     */
    orderByFieldList?: Array<''>;
}
/**
 * 查询接口返回约束接口
 */
export interface IQueryData {
    /**
     * 返回数据
     */
    data: any[];
    /**
     * 总数
     */
    total: number;
}
/**
 * 接口数据约束接口
 */
export interface IResponseResult<T> {
    header: IResponseHeader;
    data: T;
    message?: string;
}
/**
 * 接口header数据约束接口
 */
export interface IResponseHeader {
    requestId: string;
    gid: string;
    errorCode: string;
    errorMsg: string;
    success?: boolean;
}
