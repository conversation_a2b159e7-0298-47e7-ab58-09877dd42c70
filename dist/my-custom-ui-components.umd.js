(function(ce,q){typeof exports=="object"&&typeof module<"u"?q(exports,require("react"),require("antd"),require("lodash"),require("@ant-design/pro-components")):typeof define=="function"&&define.amd?define(["exports","react","antd","lodash","@ant-design/pro-components"],q):(ce=typeof globalThis<"u"?globalThis:ce||self,q(ce.MyCustomUIComponents={},ce.React,ce.antd,ce._,ce.ProComponents))})(this,function(ce,q,F,fe,wt){"use strict";function Xr(e){const t=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(e){for(const r in e)if(r!=="default"){const n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:()=>e[r]})}}return t.default=e,Object.freeze(t)}const P=Xr(q);var Zr=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function xt(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var tt={exports:{}},ke={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Mt;function Qr(){if(Mt)return ke;Mt=1;var e=q,t=Symbol.for("react.element"),r=Symbol.for("react.fragment"),n=Object.prototype.hasOwnProperty,o=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,i={key:!0,ref:!0,__self:!0,__source:!0};function s(f,c,l){var g,d={},I=null,k=null;l!==void 0&&(I=""+l),c.key!==void 0&&(I=""+c.key),c.ref!==void 0&&(k=c.ref);for(g in c)n.call(c,g)&&!i.hasOwnProperty(g)&&(d[g]=c[g]);if(f&&f.defaultProps)for(g in c=f.defaultProps,c)d[g]===void 0&&(d[g]=c[g]);return{$$typeof:t,type:f,key:I,ref:k,props:d,_owner:o.current}}return ke.Fragment=r,ke.jsx=s,ke.jsxs=s,ke}var Le={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Rt;function en(){return Rt||(Rt=1,process.env.NODE_ENV!=="production"&&function(){var e=q,t=Symbol.for("react.element"),r=Symbol.for("react.portal"),n=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),f=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),l=Symbol.for("react.suspense"),g=Symbol.for("react.suspense_list"),d=Symbol.for("react.memo"),I=Symbol.for("react.lazy"),k=Symbol.for("react.offscreen"),T=Symbol.iterator,S="@@iterator";function N(a){if(a===null||typeof a!="object")return null;var h=T&&a[T]||a[S];return typeof h=="function"?h:null}var E=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function O(a){{for(var h=arguments.length,_=new Array(h>1?h-1:0),j=1;j<h;j++)_[j-1]=arguments[j];b("error",a,_)}}function b(a,h,_){{var j=E.ReactDebugCurrentFrame,J=j.getStackAddendum();J!==""&&(h+="%s",_=_.concat([J]));var Q=_.map(function(Y){return String(Y)});Q.unshift("Warning: "+h),Function.prototype.apply.call(console[a],console,Q)}}var R=!1,u=!1,H=!1,z=!1,Z=!1,M;M=Symbol.for("react.module.reference");function $(a){return!!(typeof a=="string"||typeof a=="function"||a===n||a===i||Z||a===o||a===l||a===g||z||a===k||R||u||H||typeof a=="object"&&a!==null&&(a.$$typeof===I||a.$$typeof===d||a.$$typeof===s||a.$$typeof===f||a.$$typeof===c||a.$$typeof===M||a.getModuleId!==void 0))}function L(a,h,_){var j=a.displayName;if(j)return j;var J=h.displayName||h.name||"";return J!==""?_+"("+J+")":_}function U(a){return a.displayName||"Context"}function B(a){if(a==null)return null;if(typeof a.tag=="number"&&O("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),typeof a=="function")return a.displayName||a.name||null;if(typeof a=="string")return a;switch(a){case n:return"Fragment";case r:return"Portal";case i:return"Profiler";case o:return"StrictMode";case l:return"Suspense";case g:return"SuspenseList"}if(typeof a=="object")switch(a.$$typeof){case f:var h=a;return U(h)+".Consumer";case s:var _=a;return U(_._context)+".Provider";case c:return L(a,a.render,"ForwardRef");case d:var j=a.displayName||null;return j!==null?j:B(a.type)||"Memo";case I:{var J=a,Q=J._payload,Y=J._init;try{return B(Y(Q))}catch{return null}}}return null}var m=Object.assign,v=0,p,w,y,A,D,W,ee;function te(){}te.__reactDisabledLog=!0;function ie(){{if(v===0){p=console.log,w=console.info,y=console.warn,A=console.error,D=console.group,W=console.groupCollapsed,ee=console.groupEnd;var a={configurable:!0,enumerable:!0,value:te,writable:!0};Object.defineProperties(console,{info:a,log:a,warn:a,error:a,group:a,groupCollapsed:a,groupEnd:a})}v++}}function ue(){{if(v--,v===0){var a={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:m({},a,{value:p}),info:m({},a,{value:w}),warn:m({},a,{value:y}),error:m({},a,{value:A}),group:m({},a,{value:D}),groupCollapsed:m({},a,{value:W}),groupEnd:m({},a,{value:ee})})}v<0&&O("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}}var ge=E.ReactCurrentDispatcher,ve;function ye(a,h,_){{if(ve===void 0)try{throw Error()}catch(J){var j=J.stack.trim().match(/\n( *(at )?)/);ve=j&&j[1]||""}return`
`+ve+a}}var pe=!1,ae;{var be=typeof WeakMap=="function"?WeakMap:Map;ae=new be}function x(a,h){if(!a||pe)return"";{var _=ae.get(a);if(_!==void 0)return _}var j;pe=!0;var J=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var Q;Q=ge.current,ge.current=null,ie();try{if(h){var Y=function(){throw Error()};if(Object.defineProperty(Y.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(Y,[])}catch(me){j=me}Reflect.construct(a,[],Y)}else{try{Y.call()}catch(me){j=me}a.call(Y.prototype)}}else{try{throw Error()}catch(me){j=me}a()}}catch(me){if(me&&j&&typeof me.stack=="string"){for(var V=me.stack.split(`
`),he=j.stack.split(`
`),oe=V.length-1,se=he.length-1;oe>=1&&se>=0&&V[oe]!==he[se];)se--;for(;oe>=1&&se>=0;oe--,se--)if(V[oe]!==he[se]){if(oe!==1||se!==1)do if(oe--,se--,se<0||V[oe]!==he[se]){var Ee=`
`+V[oe].replace(" at new "," at ");return a.displayName&&Ee.includes("<anonymous>")&&(Ee=Ee.replace("<anonymous>",a.displayName)),typeof a=="function"&&ae.set(a,Ee),Ee}while(oe>=1&&se>=0);break}}}finally{pe=!1,ge.current=Q,ue(),Error.prepareStackTrace=J}var Ie=a?a.displayName||a.name:"",Re=Ie?ye(Ie):"";return typeof a=="function"&&ae.set(a,Re),Re}function Oe(a,h,_){return x(a,!1)}function Ae(a){var h=a.prototype;return!!(h&&h.isReactComponent)}function Me(a,h,_){if(a==null)return"";if(typeof a=="function")return x(a,Ae(a));if(typeof a=="string")return ye(a);switch(a){case l:return ye("Suspense");case g:return ye("SuspenseList")}if(typeof a=="object")switch(a.$$typeof){case c:return Oe(a.render);case d:return Me(a.type,h,_);case I:{var j=a,J=j._payload,Q=j._init;try{return Me(Q(J),h,_)}catch{}}}return""}var qe=Object.prototype.hasOwnProperty,kr={},Lr=E.ReactDebugCurrentFrame;function et(a){if(a){var h=a._owner,_=Me(a.type,a._source,h?h.type:null);Lr.setExtraStackFrame(_)}else Lr.setExtraStackFrame(null)}function po(a,h,_,j,J){{var Q=Function.call.bind(qe);for(var Y in a)if(Q(a,Y)){var V=void 0;try{if(typeof a[Y]!="function"){var he=Error((j||"React class")+": "+_+" type `"+Y+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof a[Y]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw he.name="Invariant Violation",he}V=a[Y](h,Y,j,_,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(oe){V=oe}V&&!(V instanceof Error)&&(et(J),O("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",j||"React class",_,Y,typeof V),et(null)),V instanceof Error&&!(V.message in kr)&&(kr[V.message]=!0,et(J),O("Failed %s type: %s",_,V.message),et(null))}}}var go=Array.isArray;function Ct(a){return go(a)}function yo(a){{var h=typeof Symbol=="function"&&Symbol.toStringTag,_=h&&a[Symbol.toStringTag]||a.constructor.name||"Object";return _}}function bo(a){try{return Fr(a),!1}catch{return!0}}function Fr(a){return""+a}function Hr(a){if(bo(a))return O("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",yo(a)),Fr(a)}var Vr=E.ReactCurrentOwner,Eo={key:!0,ref:!0,__self:!0,__source:!0},zr,Yr;function Co(a){if(qe.call(a,"ref")){var h=Object.getOwnPropertyDescriptor(a,"ref").get;if(h&&h.isReactWarning)return!1}return a.ref!==void 0}function Oo(a){if(qe.call(a,"key")){var h=Object.getOwnPropertyDescriptor(a,"key").get;if(h&&h.isReactWarning)return!1}return a.key!==void 0}function _o(a,h){typeof a.ref=="string"&&Vr.current}function To(a,h){{var _=function(){zr||(zr=!0,O("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",h))};_.isReactWarning=!0,Object.defineProperty(a,"key",{get:_,configurable:!0})}}function So(a,h){{var _=function(){Yr||(Yr=!0,O("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",h))};_.isReactWarning=!0,Object.defineProperty(a,"ref",{get:_,configurable:!0})}}var wo=function(a,h,_,j,J,Q,Y){var V={$$typeof:t,type:a,key:h,ref:_,props:Y,_owner:Q};return V._store={},Object.defineProperty(V._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(V,"_self",{configurable:!1,enumerable:!1,writable:!1,value:j}),Object.defineProperty(V,"_source",{configurable:!1,enumerable:!1,writable:!1,value:J}),Object.freeze&&(Object.freeze(V.props),Object.freeze(V)),V};function xo(a,h,_,j,J){{var Q,Y={},V=null,he=null;_!==void 0&&(Hr(_),V=""+_),Oo(h)&&(Hr(h.key),V=""+h.key),Co(h)&&(he=h.ref,_o(h,J));for(Q in h)qe.call(h,Q)&&!Eo.hasOwnProperty(Q)&&(Y[Q]=h[Q]);if(a&&a.defaultProps){var oe=a.defaultProps;for(Q in oe)Y[Q]===void 0&&(Y[Q]=oe[Q])}if(V||he){var se=typeof a=="function"?a.displayName||a.name||"Unknown":a;V&&To(Y,se),he&&So(Y,se)}return wo(a,V,he,J,j,Vr.current,Y)}}var Ot=E.ReactCurrentOwner,Br=E.ReactDebugCurrentFrame;function je(a){if(a){var h=a._owner,_=Me(a.type,a._source,h?h.type:null);Br.setExtraStackFrame(_)}else Br.setExtraStackFrame(null)}var _t;_t=!1;function Tt(a){return typeof a=="object"&&a!==null&&a.$$typeof===t}function Ur(){{if(Ot.current){var a=B(Ot.current.type);if(a)return`

Check the render method of \``+a+"`."}return""}}function Mo(a){return""}var Wr={};function Ro(a){{var h=Ur();if(!h){var _=typeof a=="string"?a:a.displayName||a.name;_&&(h=`

Check the top-level render call using <`+_+">.")}return h}}function qr(a,h){{if(!a._store||a._store.validated||a.key!=null)return;a._store.validated=!0;var _=Ro(h);if(Wr[_])return;Wr[_]=!0;var j="";a&&a._owner&&a._owner!==Ot.current&&(j=" It was passed a child from "+B(a._owner.type)+"."),je(a),O('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',_,j),je(null)}}function Gr(a,h){{if(typeof a!="object")return;if(Ct(a))for(var _=0;_<a.length;_++){var j=a[_];Tt(j)&&qr(j,h)}else if(Tt(a))a._store&&(a._store.validated=!0);else if(a){var J=N(a);if(typeof J=="function"&&J!==a.entries)for(var Q=J.call(a),Y;!(Y=Q.next()).done;)Tt(Y.value)&&qr(Y.value,h)}}}function Po(a){{var h=a.type;if(h==null||typeof h=="string")return;var _;if(typeof h=="function")_=h.propTypes;else if(typeof h=="object"&&(h.$$typeof===c||h.$$typeof===d))_=h.propTypes;else return;if(_){var j=B(h);po(_,a.props,"prop",j,a)}else if(h.PropTypes!==void 0&&!_t){_t=!0;var J=B(h);O("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?",J||"Unknown")}typeof h.getDefaultProps=="function"&&!h.getDefaultProps.isReactClassApproved&&O("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.")}}function $o(a){{for(var h=Object.keys(a.props),_=0;_<h.length;_++){var j=h[_];if(j!=="children"&&j!=="key"){je(a),O("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",j),je(null);break}}a.ref!==null&&(je(a),O("Invalid attribute `ref` supplied to `React.Fragment`."),je(null))}}var Kr={};function Jr(a,h,_,j,J,Q){{var Y=$(a);if(!Y){var V="";(a===void 0||typeof a=="object"&&a!==null&&Object.keys(a).length===0)&&(V+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var he=Mo();he?V+=he:V+=Ur();var oe;a===null?oe="null":Ct(a)?oe="array":a!==void 0&&a.$$typeof===t?(oe="<"+(B(a.type)||"Unknown")+" />",V=" Did you accidentally export a JSX literal instead of a component?"):oe=typeof a,O("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s",oe,V)}var se=xo(a,h,_,J,Q);if(se==null)return se;if(Y){var Ee=h.children;if(Ee!==void 0)if(j)if(Ct(Ee)){for(var Ie=0;Ie<Ee.length;Ie++)Gr(Ee[Ie],a);Object.freeze&&Object.freeze(Ee)}else O("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else Gr(Ee,a)}if(qe.call(h,"key")){var Re=B(a),me=Object.keys(h).filter(function(ko){return ko!=="key"}),St=me.length>0?"{key: someKey, "+me.join(": ..., ")+": ...}":"{key: someKey}";if(!Kr[Re+St]){var Io=me.length>0?"{"+me.join(": ..., ")+": ...}":"{}";O(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,St,Re,Io,Re),Kr[Re+St]=!0}}return a===n?$o(se):Po(se),se}}function No(a,h,_){return Jr(a,h,_,!0)}function Do(a,h,_){return Jr(a,h,_,!1)}var Ao=Do,jo=No;Le.Fragment=n,Le.jsx=Ao,Le.jsxs=jo}()),Le}process.env.NODE_ENV==="production"?tt.exports=Qr():tt.exports=en();var C=tt.exports;const tn=({text:e="按钮",type:t="default",onClick:r,size:n="middle"})=>{const o=fe.isString(e)?e:"按钮";return C.jsx(F.Button,{type:t,size:n,onClick:r,style:{margin:"0 4px"},children:o})},{Title:rn,Paragraph:Pt}=F.Typography,nn=({title:e,content:t,usePro:r=!1,style:n})=>{const o=fe.cloneDeep(t),i={margin:"16px",padding:"16px",...n};return r?C.jsx(wt.ProCard,{title:e,style:i,bordered:!0,children:C.jsx(Pt,{children:o})}):C.jsx(F.Card,{title:C.jsx(rn,{level:5,children:e}),style:i,bordered:!0,children:C.jsx(Pt,{children:o})})},$t={MESSAGE_CALL_MODAL:{title:"messageCallTitle"},PHONE_CALL_MODAL:{title:"phoneCallTitle"},CALL_PERRSON_MODAL:{title:"callPersonTitle"},AI_HELP_MODAL:{title:"aiHelpTitle",okText:"aiHelpOkText"},CALL_BLOCKCODE_MODAL:{title:"blockCodeTitle",width:500},CALL_DIS_CUSTOMIZE_MODAL:{title:"dispatchTitle",width:500},CALL_TRANS_CUSTOMIZE_MODAL:{title:"transferTitle",width:500}},an=({type:e,open:t,content:r,onClose:n,onSubmit:o})=>{const i=()=>{o==null||o()};return C.jsx(F.Modal,{width:"55%",open:t,centered:!0,maskClosable:!1,...$t[e],title:$t[e].title,onCancel:n,onOk:i,children:r()})},on=q.memo(an),sn="YYYY-MM-DD",cn="YYYY-MM-DD HH:mm:ss",X={create:"create",detail:"detail",copy:"copy",edit:"edit",delete:"delete",list:"list",cancel:"cancel",save:"save",up:"up",down:"down",submit:"submit",flowNode:"flowNode",dispatch:"dispatch",dowload:"dowload",aiHelp:"aiHelp",menuAuth:"menuAuth",userDetail:"userDetail",fieldManage:"fieldManage"},Ge={THEME:"theme"},ln={EN:{locale:"en"}},Se={INPUT:"Input",CHECK_BOX:"Checkbox",SELECT:"Select",TREE_SELECT:"TreeSelect",CASCADER:"Cascader",DATE_PICKER:"DatePicker",RANGE_PICKER:"RangePicker"},Pe={Amount:"Amount",Dictionary:"Dictionary",MockDictionary:"MockDictionary",Ellipsis:"ellipsis",DateTime:"DateTime",Date:"Date"},rt={WARNING:"warning"},un={SELECT:"select"},fn={CHECK_MSG:"checkMsg",EDITING:"formEditing"},_e={COMMON:"common"},dn={TYPE:"type",VALUE:"value",LABEL:"label",RULES:"rules",DATA:"data",SHOWKEY:"showKey",REF:"ref",BIND:"bind",PREFIX:"prefix",HIDE:"hide",DEPENDENCIES:"dependencies",DICTtYPE:"dictType",FORMLAYOUTTYPE:"formlayoutType",COL_SPAN:"colSpan",PARSER:"parser",SHOULD_UPDATE:"shouldUpdate",SET_VISIBLE_FUNC:"setVisibleFunc"};var Nt=q.createContext({});function re(){return re=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},re.apply(null,arguments)}function hn(e){if(Array.isArray(e))return e}function mn(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,o,i,s,f=[],c=!0,l=!1;try{if(i=(r=r.call(e)).next,t!==0)for(;!(c=(n=i.call(r)).done)&&(f.push(n.value),f.length!==t);c=!0);}catch(g){l=!0,o=g}finally{try{if(!c&&r.return!=null&&(s=r.return(),Object(s)!==s))return}finally{if(l)throw o}}return f}}function Dt(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function vn(e,t){if(e){if(typeof e=="string")return Dt(e,t);var r={}.toString.call(e).slice(8,-1);return r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set"?Array.from(e):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Dt(e,t):void 0}}function pn(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function At(e,t){return hn(e)||mn(e,t)||vn(e,t)||pn()}function we(e){"@babel/helpers - typeof";return we=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},we(e)}function gn(e,t){if(we(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(we(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function yn(e){var t=gn(e,"string");return we(t)=="symbol"?t:t+""}function de(e,t,r){return(t=yn(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function bn(e,t){if(e==null)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.indexOf(n)!==-1)continue;r[n]=e[n]}return r}function jt(e,t){if(e==null)return{};var r,n,o=bn(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)===-1&&{}.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}var It={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/(function(e){(function(){var t={}.hasOwnProperty;function r(){for(var i="",s=0;s<arguments.length;s++){var f=arguments[s];f&&(i=o(i,n(f)))}return i}function n(i){if(typeof i=="string"||typeof i=="number")return i;if(typeof i!="object")return"";if(Array.isArray(i))return r.apply(null,i);if(i.toString!==Object.prototype.toString&&!i.toString.toString().includes("[native code]"))return i.toString();var s="";for(var f in i)t.call(i,f)&&i[f]&&(s=o(s,f));return s}function o(i,s){return s?i?i+" "+s:i+s:i}e.exports?(r.default=r,e.exports=r):window.classNames=r})()})(It);var En=It.exports;const Cn=xt(En),le=Math.round;function nt(e,t){const r=e.replace(/^[^(]*\((.*)/,"$1").replace(/\).*/,"").match(/\d*\.?\d+%?/g)||[],n=r.map(o=>parseFloat(o));for(let o=0;o<3;o+=1)n[o]=t(n[o]||0,r[o]||"",o);return r[3]?n[3]=r[3].includes("%")?n[3]/100:n[3]:n[3]=1,n}const kt=(e,t,r)=>r===0?e:e/100;function Fe(e,t){const r=t||255;return e>r?r:e<0?0:e}class $e{constructor(t){de(this,"isValid",!0),de(this,"r",0),de(this,"g",0),de(this,"b",0),de(this,"a",1),de(this,"_h",void 0),de(this,"_s",void 0),de(this,"_l",void 0),de(this,"_v",void 0),de(this,"_max",void 0),de(this,"_min",void 0),de(this,"_brightness",void 0);function r(n){return n[0]in t&&n[1]in t&&n[2]in t}if(t)if(typeof t=="string"){let o=function(i){return n.startsWith(i)};const n=t.trim();/^#?[A-F\d]{3,8}$/i.test(n)?this.fromHexString(n):o("rgb")?this.fromRgbString(n):o("hsl")?this.fromHslString(n):(o("hsv")||o("hsb"))&&this.fromHsvString(n)}else if(t instanceof $e)this.r=t.r,this.g=t.g,this.b=t.b,this.a=t.a,this._h=t._h,this._s=t._s,this._l=t._l,this._v=t._v;else if(r("rgb"))this.r=Fe(t.r),this.g=Fe(t.g),this.b=Fe(t.b),this.a=typeof t.a=="number"?Fe(t.a,1):1;else if(r("hsl"))this.fromHsl(t);else if(r("hsv"))this.fromHsv(t);else throw new Error("@ant-design/fast-color: unsupported input "+JSON.stringify(t))}setR(t){return this._sc("r",t)}setG(t){return this._sc("g",t)}setB(t){return this._sc("b",t)}setA(t){return this._sc("a",t,1)}setHue(t){const r=this.toHsv();return r.h=t,this._c(r)}getLuminance(){function t(i){const s=i/255;return s<=.03928?s/12.92:Math.pow((s+.055)/1.055,2.4)}const r=t(this.r),n=t(this.g),o=t(this.b);return .2126*r+.7152*n+.0722*o}getHue(){if(typeof this._h>"u"){const t=this.getMax()-this.getMin();t===0?this._h=0:this._h=le(60*(this.r===this.getMax()?(this.g-this.b)/t+(this.g<this.b?6:0):this.g===this.getMax()?(this.b-this.r)/t+2:(this.r-this.g)/t+4))}return this._h}getSaturation(){if(typeof this._s>"u"){const t=this.getMax()-this.getMin();t===0?this._s=0:this._s=t/this.getMax()}return this._s}getLightness(){return typeof this._l>"u"&&(this._l=(this.getMax()+this.getMin())/510),this._l}getValue(){return typeof this._v>"u"&&(this._v=this.getMax()/255),this._v}getBrightness(){return typeof this._brightness>"u"&&(this._brightness=(this.r*299+this.g*587+this.b*114)/1e3),this._brightness}darken(t=10){const r=this.getHue(),n=this.getSaturation();let o=this.getLightness()-t/100;return o<0&&(o=0),this._c({h:r,s:n,l:o,a:this.a})}lighten(t=10){const r=this.getHue(),n=this.getSaturation();let o=this.getLightness()+t/100;return o>1&&(o=1),this._c({h:r,s:n,l:o,a:this.a})}mix(t,r=50){const n=this._c(t),o=r/100,i=f=>(n[f]-this[f])*o+this[f],s={r:le(i("r")),g:le(i("g")),b:le(i("b")),a:le(i("a")*100)/100};return this._c(s)}tint(t=10){return this.mix({r:255,g:255,b:255,a:1},t)}shade(t=10){return this.mix({r:0,g:0,b:0,a:1},t)}onBackground(t){const r=this._c(t),n=this.a+r.a*(1-this.a),o=i=>le((this[i]*this.a+r[i]*r.a*(1-this.a))/n);return this._c({r:o("r"),g:o("g"),b:o("b"),a:n})}isDark(){return this.getBrightness()<128}isLight(){return this.getBrightness()>=128}equals(t){return this.r===t.r&&this.g===t.g&&this.b===t.b&&this.a===t.a}clone(){return this._c(this)}toHexString(){let t="#";const r=(this.r||0).toString(16);t+=r.length===2?r:"0"+r;const n=(this.g||0).toString(16);t+=n.length===2?n:"0"+n;const o=(this.b||0).toString(16);if(t+=o.length===2?o:"0"+o,typeof this.a=="number"&&this.a>=0&&this.a<1){const i=le(this.a*255).toString(16);t+=i.length===2?i:"0"+i}return t}toHsl(){return{h:this.getHue(),s:this.getSaturation(),l:this.getLightness(),a:this.a}}toHslString(){const t=this.getHue(),r=le(this.getSaturation()*100),n=le(this.getLightness()*100);return this.a!==1?`hsla(${t},${r}%,${n}%,${this.a})`:`hsl(${t},${r}%,${n}%)`}toHsv(){return{h:this.getHue(),s:this.getSaturation(),v:this.getValue(),a:this.a}}toRgb(){return{r:this.r,g:this.g,b:this.b,a:this.a}}toRgbString(){return this.a!==1?`rgba(${this.r},${this.g},${this.b},${this.a})`:`rgb(${this.r},${this.g},${this.b})`}toString(){return this.toRgbString()}_sc(t,r,n){const o=this.clone();return o[t]=Fe(r,n),o}_c(t){return new this.constructor(t)}getMax(){return typeof this._max>"u"&&(this._max=Math.max(this.r,this.g,this.b)),this._max}getMin(){return typeof this._min>"u"&&(this._min=Math.min(this.r,this.g,this.b)),this._min}fromHexString(t){const r=t.replace("#","");function n(o,i){return parseInt(r[o]+r[i||o],16)}r.length<6?(this.r=n(0),this.g=n(1),this.b=n(2),this.a=r[3]?n(3)/255:1):(this.r=n(0,1),this.g=n(2,3),this.b=n(4,5),this.a=r[6]?n(6,7)/255:1)}fromHsl({h:t,s:r,l:n,a:o}){if(this._h=t%360,this._s=r,this._l=n,this.a=typeof o=="number"?o:1,r<=0){const I=le(n*255);this.r=I,this.g=I,this.b=I}let i=0,s=0,f=0;const c=t/60,l=(1-Math.abs(2*n-1))*r,g=l*(1-Math.abs(c%2-1));c>=0&&c<1?(i=l,s=g):c>=1&&c<2?(i=g,s=l):c>=2&&c<3?(s=l,f=g):c>=3&&c<4?(s=g,f=l):c>=4&&c<5?(i=g,f=l):c>=5&&c<6&&(i=l,f=g);const d=n-l/2;this.r=le((i+d)*255),this.g=le((s+d)*255),this.b=le((f+d)*255)}fromHsv({h:t,s:r,v:n,a:o}){this._h=t%360,this._s=r,this._v=n,this.a=typeof o=="number"?o:1;const i=le(n*255);if(this.r=i,this.g=i,this.b=i,r<=0)return;const s=t/60,f=Math.floor(s),c=s-f,l=le(n*(1-r)*255),g=le(n*(1-r*c)*255),d=le(n*(1-r*(1-c))*255);switch(f){case 0:this.g=d,this.b=l;break;case 1:this.r=g,this.b=l;break;case 2:this.r=l,this.b=d;break;case 3:this.r=l,this.g=g;break;case 4:this.r=d,this.g=l;break;case 5:default:this.g=l,this.b=g;break}}fromHsvString(t){const r=nt(t,kt);this.fromHsv({h:r[0],s:r[1],v:r[2],a:r[3]})}fromHslString(t){const r=nt(t,kt);this.fromHsl({h:r[0],s:r[1],l:r[2],a:r[3]})}fromRgbString(t){const r=nt(t,(n,o)=>o.includes("%")?le(n/100*255):n);this.r=r[0],this.g=r[1],this.b=r[2],this.a=r[3]}}var Ke=2,Lt=.16,On=.05,_n=.05,Tn=.15,Ft=5,Ht=4,Sn=[{index:7,amount:15},{index:6,amount:25},{index:5,amount:30},{index:5,amount:45},{index:5,amount:65},{index:5,amount:85},{index:4,amount:90},{index:3,amount:95},{index:2,amount:97},{index:1,amount:98}];function Vt(e,t,r){var n;return Math.round(e.h)>=60&&Math.round(e.h)<=240?n=r?Math.round(e.h)-Ke*t:Math.round(e.h)+Ke*t:n=r?Math.round(e.h)+Ke*t:Math.round(e.h)-Ke*t,n<0?n+=360:n>=360&&(n-=360),n}function zt(e,t,r){if(e.h===0&&e.s===0)return e.s;var n;return r?n=e.s-Lt*t:t===Ht?n=e.s+Lt:n=e.s+On*t,n>1&&(n=1),r&&t===Ft&&n>.1&&(n=.1),n<.06&&(n=.06),Math.round(n*100)/100}function Yt(e,t,r){var n;return r?n=e.v+_n*t:n=e.v-Tn*t,n=Math.max(0,Math.min(1,n)),Math.round(n*100)/100}function wn(e){for(var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=[],n=new $e(e),o=n.toHsv(),i=Ft;i>0;i-=1){var s=new $e({h:Vt(o,i,!0),s:zt(o,i,!0),v:Yt(o,i,!0)});r.push(s)}r.push(n);for(var f=1;f<=Ht;f+=1){var c=new $e({h:Vt(o,f),s:zt(o,f),v:Yt(o,f)});r.push(c)}return t.theme==="dark"?Sn.map(function(l){var g=l.index,d=l.amount;return new $e(t.backgroundColor||"#141414").mix(r[g],d).toHexString()}):r.map(function(l){return l.toHexString()})}var at=["#e6f4ff","#bae0ff","#91caff","#69b1ff","#4096ff","#1677ff","#0958d9","#003eb3","#002c8c","#001d66"];at.primary=at[5];function Bt(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function Ce(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Bt(Object(r),!0).forEach(function(n){de(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Bt(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function xn(){return!!(typeof window<"u"&&window.document&&window.document.createElement)}function Mn(e,t){if(!e)return!1;if(e.contains)return e.contains(t);for(var r=t;r;){if(r===e)return!0;r=r.parentNode}return!1}var Ut="data-rc-order",Wt="data-rc-priority",Rn="rc-util-key",ot=new Map;function qt(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=e.mark;return t?t.startsWith("data-")?t:"data-".concat(t):Rn}function it(e){if(e.attachTo)return e.attachTo;var t=document.querySelector("head");return t||document.body}function Pn(e){return e==="queue"?"prependQueue":e?"prepend":"append"}function st(e){return Array.from((ot.get(e)||e).children).filter(function(t){return t.tagName==="STYLE"})}function Gt(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(!xn())return null;var r=t.csp,n=t.prepend,o=t.priority,i=o===void 0?0:o,s=Pn(n),f=s==="prependQueue",c=document.createElement("style");c.setAttribute(Ut,s),f&&i&&c.setAttribute(Wt,"".concat(i)),r!=null&&r.nonce&&(c.nonce=r==null?void 0:r.nonce),c.innerHTML=e;var l=it(t),g=l.firstChild;if(n){if(f){var d=(t.styles||st(l)).filter(function(I){if(!["prepend","prependQueue"].includes(I.getAttribute(Ut)))return!1;var k=Number(I.getAttribute(Wt)||0);return i>=k});if(d.length)return l.insertBefore(c,d[d.length-1].nextSibling),c}l.insertBefore(c,g)}else l.appendChild(c);return c}function $n(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=it(t);return(t.styles||st(r)).find(function(n){return n.getAttribute(qt(t))===e})}function Nn(e,t){var r=ot.get(e);if(!r||!Mn(document,r)){var n=Gt("",t),o=n.parentNode;ot.set(e,o),e.removeChild(n)}}function Dn(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},n=it(r),o=st(n),i=Ce(Ce({},r),{},{styles:o});Nn(n,i);var s=$n(t,i);if(s){var f,c;if((f=i.csp)!==null&&f!==void 0&&f.nonce&&s.nonce!==((c=i.csp)===null||c===void 0?void 0:c.nonce)){var l;s.nonce=(l=i.csp)===null||l===void 0?void 0:l.nonce}return s.innerHTML!==e&&(s.innerHTML=e),s}var g=Gt(e,i);return g.setAttribute(qt(i),t),g}function Kt(e){var t;return e==null||(t=e.getRootNode)===null||t===void 0?void 0:t.call(e)}function An(e){return Kt(e)instanceof ShadowRoot}function jn(e){return An(e)?Kt(e):null}var ct={},lt=[],In=function(t){lt.push(t)};function kn(e,t){if(process.env.NODE_ENV!=="production"&&!e&&console!==void 0){var r=lt.reduce(function(n,o){return o(n??"","warning")},t);r&&console.error("Warning: ".concat(r))}}function Ln(e,t){if(process.env.NODE_ENV!=="production"&&!e&&console!==void 0){var r=lt.reduce(function(n,o){return o(n??"","note")},t);r&&console.warn("Note: ".concat(r))}}function Fn(){ct={}}function Jt(e,t,r){!t&&!ct[r]&&(e(!1,r),ct[r]=!0)}function Je(e,t){Jt(kn,e,t)}function Hn(e,t){Jt(Ln,e,t)}Je.preMessage=In,Je.resetWarned=Fn,Je.noteOnce=Hn;function Vn(e){return e.replace(/-(.)/g,function(t,r){return r.toUpperCase()})}function zn(e,t){Je(e,"[@ant-design/icons] ".concat(t))}function Xt(e){return we(e)==="object"&&typeof e.name=="string"&&typeof e.theme=="string"&&(we(e.icon)==="object"||typeof e.icon=="function")}function Zt(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return Object.keys(e).reduce(function(t,r){var n=e[r];switch(r){case"class":t.className=n,delete t.class;break;default:delete t[r],t[Vn(r)]=n}return t},{})}function ut(e,t,r){return r?q.createElement(e.tag,Ce(Ce({key:t},Zt(e.attrs)),r),(e.children||[]).map(function(n,o){return ut(n,"".concat(t,"-").concat(e.tag,"-").concat(o))})):q.createElement(e.tag,Ce({key:t},Zt(e.attrs)),(e.children||[]).map(function(n,o){return ut(n,"".concat(t,"-").concat(e.tag,"-").concat(o))}))}function Qt(e){return wn(e)[0]}function er(e){return e?Array.isArray(e)?e:[e]:[]}var Yn=`
.anticon {
  display: inline-flex;
  align-items: center;
  color: inherit;
  font-style: normal;
  line-height: 0;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.anticon > * {
  line-height: 1;
}

.anticon svg {
  display: inline-block;
}

.anticon::before {
  display: none;
}

.anticon .anticon-icon {
  display: block;
}

.anticon[tabindex] {
  cursor: pointer;
}

.anticon-spin::before,
.anticon-spin {
  display: inline-block;
  -webkit-animation: loadingCircle 1s infinite linear;
  animation: loadingCircle 1s infinite linear;
}

@-webkit-keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
`,Bn=function(t){var r=q.useContext(Nt),n=r.csp,o=r.prefixCls,i=r.layer,s=Yn;o&&(s=s.replace(/anticon/g,o)),i&&(s="@layer ".concat(i,` {
`).concat(s,`
}`)),q.useEffect(function(){var f=t.current,c=jn(f);Dn(s,"@ant-design-icons",{prepend:!i,csp:n,attachTo:c})},[])},Un=["icon","className","onClick","style","primaryColor","secondaryColor"],He={primaryColor:"#333",secondaryColor:"#E6E6E6",calculated:!1};function Wn(e){var t=e.primaryColor,r=e.secondaryColor;He.primaryColor=t,He.secondaryColor=r||Qt(t),He.calculated=!!r}function qn(){return Ce({},He)}var Ne=function(t){var r=t.icon,n=t.className,o=t.onClick,i=t.style,s=t.primaryColor,f=t.secondaryColor,c=jt(t,Un),l=P.useRef(),g=He;if(s&&(g={primaryColor:s,secondaryColor:f||Qt(s)}),Bn(l),zn(Xt(r),"icon should be icon definiton, but got ".concat(r)),!Xt(r))return null;var d=r;return d&&typeof d.icon=="function"&&(d=Ce(Ce({},d),{},{icon:d.icon(g.primaryColor,g.secondaryColor)})),ut(d.icon,"svg-".concat(d.name),Ce(Ce({className:n,onClick:o,style:i,"data-icon":d.name,width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},c),{},{ref:l}))};Ne.displayName="IconReact",Ne.getTwoToneColors=qn,Ne.setTwoToneColors=Wn;function tr(e){var t=er(e),r=At(t,2),n=r[0],o=r[1];return Ne.setTwoToneColors({primaryColor:n,secondaryColor:o})}function Gn(){var e=Ne.getTwoToneColors();return e.calculated?[e.primaryColor,e.secondaryColor]:e.primaryColor}var Kn=["className","icon","spin","rotate","tabIndex","onClick","twoToneColor"];tr(at.primary);var ne=P.forwardRef(function(e,t){var r=e.className,n=e.icon,o=e.spin,i=e.rotate,s=e.tabIndex,f=e.onClick,c=e.twoToneColor,l=jt(e,Kn),g=P.useContext(Nt),d=g.prefixCls,I=d===void 0?"anticon":d,k=g.rootClassName,T=Cn(k,I,de(de({},"".concat(I,"-").concat(n.name),!!n.name),"".concat(I,"-spin"),!!o||n.name==="loading"),r),S=s;S===void 0&&f&&(S=-1);var N=i?{msTransform:"rotate(".concat(i,"deg)"),transform:"rotate(".concat(i,"deg)")}:void 0,E=er(c),O=At(E,2),b=O[0],R=O[1];return P.createElement("span",re({role:"img","aria-label":n.name},l,{ref:t,tabIndex:S,onClick:f,className:T}),P.createElement(Ne,{icon:n,primaryColor:b,secondaryColor:R,style:N}))});ne.displayName="AntdIcon",ne.getTwoToneColor=Gn,ne.setTwoToneColor=tr;var Jn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908 640H804V488c0-4.4-3.6-8-8-8H548v-96h108c8.8 0 16-7.2 16-16V80c0-8.8-7.2-16-16-16H368c-8.8 0-16 7.2-16 16v288c0 8.8 7.2 16 16 16h108v96H228c-4.4 0-8 3.6-8 8v152H116c-8.8 0-16 7.2-16 16v288c0 8.8 7.2 16 16 16h288c8.8 0 16-7.2 16-16V656c0-8.8-7.2-16-16-16H292v-88h440v88H620c-8.8 0-16 7.2-16 16v288c0 8.8 7.2 16 16 16h288c8.8 0 16-7.2 16-16V656c0-8.8-7.2-16-16-16zm-564 76v168H176V716h168zm84-408V140h168v168H428zm420 576H680V716h168v168z"}}]},name:"apartment",theme:"outlined"},Xn=function(t,r){return P.createElement(ne,re({},t,{ref:r,icon:Jn}))},rr=P.forwardRef(Xn);process.env.NODE_ENV!=="production"&&(rr.displayName="ApartmentOutlined");var Zn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M862 465.3h-81c-4.6 0-9 2-12.1 5.5L550 723.1V160c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v563.1L255.1 470.8c-3-3.5-7.4-5.5-12.1-5.5h-81c-6.8 0-10.5 8.1-6 13.2L487.9 861a31.96 31.96 0 0048.3 0L868 478.5c4.5-5.2.8-13.2-6-13.2z"}}]},name:"arrow-down",theme:"outlined"},Qn=function(t,r){return P.createElement(ne,re({},t,{ref:r,icon:Zn}))},nr=P.forwardRef(Qn);process.env.NODE_ENV!=="production"&&(nr.displayName="ArrowDownOutlined");var ea={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 545.5L536.1 163a31.96 31.96 0 00-48.3 0L156 545.5a7.97 7.97 0 006 13.2h81c4.6 0 9-2 12.1-5.5L474 300.9V864c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V300.9l218.9 252.3c3 3.5 7.4 5.5 12.1 5.5h81c6.8 0 10.5-8 6-13.2z"}}]},name:"arrow-up",theme:"outlined"},ta=function(t,r){return P.createElement(ne,re({},t,{ref:r,icon:ea}))},ar=P.forwardRef(ta);process.env.NODE_ENV!=="production"&&(ar.displayName="ArrowUpOutlined");var ra={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"}}]},name:"close",theme:"outlined"},na=function(t,r){return P.createElement(ne,re({},t,{ref:r,icon:ra}))},or=P.forwardRef(na);process.env.NODE_ENV!=="production"&&(or.displayName="CloseOutlined");var aa={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M888 680h-54V540H546v-92h238c8.8 0 16-7.2 16-16V168c0-8.8-7.2-16-16-16H240c-8.8 0-16 7.2-16 16v264c0 8.8 7.2 16 16 16h238v92H190v140h-54c-4.4 0-8 3.6-8 8v176c0 4.4 3.6 8 8 8h176c4.4 0 8-3.6 8-8V688c0-4.4-3.6-8-8-8h-54v-72h220v72h-54c-4.4 0-8 3.6-8 8v176c0 4.4 3.6 8 8 8h176c4.4 0 8-3.6 8-8V688c0-4.4-3.6-8-8-8h-54v-72h220v72h-54c-4.4 0-8 3.6-8 8v176c0 4.4 3.6 8 8 8h176c4.4 0 8-3.6 8-8V688c0-4.4-3.6-8-8-8zM256 805.3c0 1.5-1.2 2.7-2.7 2.7h-58.7c-1.5 0-2.7-1.2-2.7-2.7v-58.7c0-1.5 1.2-2.7 2.7-2.7h58.7c1.5 0 2.7 1.2 2.7 2.7v58.7zm288 0c0 1.5-1.2 2.7-2.7 2.7h-58.7c-1.5 0-2.7-1.2-2.7-2.7v-58.7c0-1.5 1.2-2.7 2.7-2.7h58.7c1.5 0 2.7 1.2 2.7 2.7v58.7zM288 384V216h448v168H288zm544 421.3c0 1.5-1.2 2.7-2.7 2.7h-58.7c-1.5 0-2.7-1.2-2.7-2.7v-58.7c0-1.5 1.2-2.7 2.7-2.7h58.7c1.5 0 2.7 1.2 2.7 2.7v58.7zM360 300a40 40 0 1080 0 40 40 0 10-80 0z"}}]},name:"cluster",theme:"outlined"},oa=function(t,r){return P.createElement(ne,re({},t,{ref:r,icon:aa}))},ir=P.forwardRef(oa);process.env.NODE_ENV!=="production"&&(ir.displayName="ClusterOutlined");var ia={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"}}]},name:"copy",theme:"outlined"},sa=function(t,r){return P.createElement(ne,re({},t,{ref:r,icon:ia}))},sr=P.forwardRef(sa);process.env.NODE_ENV!=="production"&&(sr.displayName="CopyOutlined");var ca={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"},la=function(t,r){return P.createElement(ne,re({},t,{ref:r,icon:ca}))},cr=P.forwardRef(la);process.env.NODE_ENV!=="production"&&(cr.displayName="DeleteOutlined");var ua={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"download",theme:"outlined"},fa=function(t,r){return P.createElement(ne,re({},t,{ref:r,icon:ua}))},lr=P.forwardRef(fa);process.env.NODE_ENV!=="production"&&(lr.displayName="DownloadOutlined");var da={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M942.2 486.2C847.4 286.5 704.1 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 000 51.5C176.6 737.5 319.9 838 512 838c192.2 0 335.4-100.5 430.2-300.3 7.7-16.2 7.7-35 0-51.5zM512 766c-161.3 0-279.4-81.8-362.7-254C232.6 339.8 350.7 258 512 258c161.3 0 279.4 81.8 362.7 254C791.5 684.2 673.4 766 512 766zm-4-430c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z"}}]},name:"eye",theme:"outlined"},ha=function(t,r){return P.createElement(ne,re({},t,{ref:r,icon:da}))},ur=P.forwardRef(ha);process.env.NODE_ENV!=="production"&&(ur.displayName="EyeOutlined");var ma={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M945 412H689c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h256c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zM811 548H689c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h122c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zM477.3 322.5H434c-6.2 0-11.2 5-11.2 11.2v248c0 3.6 1.7 6.9 4.6 9l148.9 108.6c5 3.6 12 2.6 15.6-2.4l25.7-35.1v-.1c3.6-5 2.5-12-2.5-15.6l-126.7-91.6V333.7c.1-6.2-5-11.2-11.1-11.2z"}},{tag:"path",attrs:{d:"M804.8 673.9H747c-5.6 0-10.9 2.9-13.9 7.7a321 321 0 01-44.5 55.7 317.17 317.17 0 01-101.3 68.3c-39.3 16.6-81 25-124 25-43.1 0-84.8-8.4-124-25-37.9-16-72-39-101.3-68.3s-52.3-63.4-68.3-101.3c-16.6-39.2-25-80.9-25-124 0-43.1 8.4-84.7 25-124 16-37.9 39-72 68.3-101.3 29.3-29.3 63.4-52.3 101.3-68.3 39.2-16.6 81-25 124-25 43.1 0 84.8 8.4 124 25 37.9 16 72 39 101.3 68.3a321 321 0 0144.5 55.7c3 4.8 8.3 7.7 13.9 7.7h57.8c6.9 0 11.3-7.2 8.2-13.3-65.2-129.7-197.4-214-345-215.7-216.1-2.7-395.6 174.2-396 390.1C71.6 727.5 246.9 903 463.2 903c149.5 0 283.9-84.6 349.8-215.8a9.18 9.18 0 00-8.2-13.3z"}}]},name:"field-time",theme:"outlined"},va=function(t,r){return P.createElement(ne,re({},t,{ref:r,icon:ma}))},fr=P.forwardRef(va);process.env.NODE_ENV!=="production"&&(fr.displayName="FieldTimeOutlined");var pa={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M688 312v-48c0-4.4-3.6-8-8-8H296c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h384c4.4 0 8-3.6 8-8zm-392 88c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H296zm144 452H208V148h560v344c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V108c0-17.7-14.3-32-32-32H168c-17.7 0-32 14.3-32 32v784c0 17.7 14.3 32 32 32h272c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm445.7 51.5l-93.3-93.3C814.7 780.7 828 743.9 828 704c0-97.2-78.8-176-176-176s-176 78.8-176 176 78.8 176 176 176c35.8 0 69-10.7 96.8-29l94.7 94.7c1.6 1.6 3.6 2.3 5.6 2.3s4.1-.8 5.6-2.3l31-31a7.9 7.9 0 000-11.2zM652 816c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z"}}]},name:"file-search",theme:"outlined"},ga=function(t,r){return P.createElement(ne,re({},t,{ref:r,icon:pa}))},dr=P.forwardRef(ga);process.env.NODE_ENV!=="production"&&(dr.displayName="FileSearchOutlined");var ya={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M904 512h-56c-4.4 0-8 3.6-8 8v320H184V184h320c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V520c0-4.4-3.6-8-8-8z"}},{tag:"path",attrs:{d:"M355.9 534.9L354 653.8c-.1 8.9 7.1 16.2 16 16.2h.4l118-2.9c2-.1 4-.9 5.4-2.3l415.9-415c3.1-3.1 3.1-8.2 0-11.3L785.4 114.3c-1.6-1.6-3.6-2.3-5.7-2.3s-4.1.8-5.7 2.3l-415.8 415a8.3 8.3 0 00-2.3 5.6zm63.5 23.6L779.7 199l45.2 45.1-360.5 359.7-45.7 1.1.7-46.4z"}}]},name:"form",theme:"outlined"},ba=function(t,r){return P.createElement(ne,re({},t,{ref:r,icon:ya}))},ft=P.forwardRef(ba);process.env.NODE_ENV!=="production"&&(ft.displayName="FormOutlined");var Ea={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM142.4 642.1L298.7 519a8.84 8.84 0 000-13.9L142.4 381.9c-5.8-4.6-14.4-.5-14.4 6.9v246.3a8.9 8.9 0 0014.4 7z"}}]},name:"menu-unfold",theme:"outlined"},Ca=function(t,r){return P.createElement(ne,re({},t,{ref:r,icon:Ea}))},hr=P.forwardRef(Ca);process.env.NODE_ENV!=="production"&&(hr.displayName="MenuUnfoldOutlined");var Oa={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"}},{tag:"path",attrs:{d:"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"}}]},name:"plus",theme:"outlined"},_a=function(t,r){return P.createElement(ne,re({},t,{ref:r,icon:Oa}))},mr=P.forwardRef(_a);process.env.NODE_ENV!=="production"&&(mr.displayName="PlusOutlined");var Ta={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M758.2 839.1C851.8 765.9 912 651.9 912 523.9 912 303 733.5 124.3 512.6 124 291.4 123.7 112 302.8 112 523.9c0 125.2 57.5 236.9 147.6 310.2 3.5 2.8 8.6 2.2 11.4-1.3l39.4-50.5c2.7-3.4 2.1-8.3-1.2-11.1-8.1-6.6-15.9-13.7-23.4-21.2a318.64 318.64 0 01-68.6-101.7C200.4 609 192 567.1 192 523.9s8.4-85.1 25.1-124.5c16.1-38.1 39.2-72.3 68.6-101.7 29.4-29.4 63.6-52.5 101.7-68.6C426.9 212.4 468.8 204 512 204s85.1 8.4 124.5 25.1c38.1 16.1 72.3 39.2 101.7 68.6 29.4 29.4 52.5 63.6 68.6 101.7 16.7 39.4 25.1 81.3 25.1 124.5s-8.4 85.1-25.1 124.5a318.64 318.64 0 01-68.6 101.7c-9.3 9.3-19.1 18-29.3 26L668.2 724a8 8 0 00-14.1 3l-39.6 162.2c-1.2 5 2.6 9.9 7.7 9.9l167 .8c6.7 0 10.5-7.7 6.3-12.9l-37.3-47.9z"}}]},name:"redo",theme:"outlined"},Sa=function(t,r){return P.createElement(ne,re({},t,{ref:r,icon:Ta}))},vr=P.forwardRef(Sa);process.env.NODE_ENV!=="production"&&(vr.displayName="RedoOutlined");var wa={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M893.3 293.3L730.7 130.7c-7.5-7.5-16.7-13-26.7-16V112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V338.5c0-17-6.7-33.2-18.7-45.2zM384 184h256v104H384V184zm456 656H184V184h136v136c0 17.7 14.3 32 32 32h320c17.7 0 32-14.3 32-32V205.8l136 136V840zM512 442c-79.5 0-144 64.5-144 144s64.5 144 144 144 144-64.5 144-144-64.5-144-144-144zm0 224c-44.2 0-80-35.8-80-80s35.8-80 80-80 80 35.8 80 80-35.8 80-80 80z"}}]},name:"save",theme:"outlined"},xa=function(t,r){return P.createElement(ne,re({},t,{ref:r,icon:wa}))},pr=P.forwardRef(xa);process.env.NODE_ENV!=="production"&&(pr.displayName="SaveOutlined");var Ma={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"}}]},name:"search",theme:"outlined"},Ra=function(t,r){return P.createElement(ne,re({},t,{ref:r,icon:Ma}))},dt=P.forwardRef(Ra);process.env.NODE_ENV!=="production"&&(dt.displayName="SearchOutlined");var Pa={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M931.4 498.9L94.9 79.5c-3.4-1.7-7.3-2.1-11-1.2a15.99 15.99 0 00-11.7 19.3l86.2 352.2c1.3 5.3 5.2 9.6 10.4 11.3l147.7 50.7-147.6 50.7c-5.2 1.8-9.1 6-10.3 11.3L72.2 926.5c-.9 3.7-.5 7.6 1.2 10.9 3.9 7.9 13.5 11.1 21.5 7.2l836.5-417c3.1-1.5 5.6-4.1 7.2-7.1 3.9-8 .7-17.6-7.2-21.6zM170.8 826.3l50.3-205.6 295.2-101.3c2.3-.8 4.2-2.6 5-5 1.4-4.2-.8-8.7-5-10.2L221.1 403 171 198.2l628 314.9-628.2 313.2z"}}]},name:"send",theme:"outlined"},$a=function(t,r){return P.createElement(ne,re({},t,{ref:r,icon:Pa}))},gr=P.forwardRef($a);process.env.NODE_ENV!=="production"&&(gr.displayName="SendOutlined");var Na={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M400 317.7h73.9V656c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V317.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 163a8 8 0 00-12.6 0l-112 141.7c-4.1 5.3-.4 13 6.3 13zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"upload",theme:"outlined"},Da=function(t,r){return P.createElement(ne,re({},t,{ref:r,icon:Na}))},yr=P.forwardRef(Da);process.env.NODE_ENV!=="production"&&(yr.displayName="UploadOutlined");var br={exports:{}};(function(e,t){(function(r,n){e.exports=n()})(Zr,function(){var r=1e3,n=6e4,o=36e5,i="millisecond",s="second",f="minute",c="hour",l="day",g="week",d="month",I="quarter",k="year",T="date",S="Invalid Date",N=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,E=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,O={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(m){var v=["th","st","nd","rd"],p=m%100;return"["+m+(v[(p-20)%10]||v[p]||v[0])+"]"}},b=function(m,v,p){var w=String(m);return!w||w.length>=v?m:""+Array(v+1-w.length).join(p)+m},R={s:b,z:function(m){var v=-m.utcOffset(),p=Math.abs(v),w=Math.floor(p/60),y=p%60;return(v<=0?"+":"-")+b(w,2,"0")+":"+b(y,2,"0")},m:function m(v,p){if(v.date()<p.date())return-m(p,v);var w=12*(p.year()-v.year())+(p.month()-v.month()),y=v.clone().add(w,d),A=p-y<0,D=v.clone().add(w+(A?-1:1),d);return+(-(w+(p-y)/(A?y-D:D-y))||0)},a:function(m){return m<0?Math.ceil(m)||0:Math.floor(m)},p:function(m){return{M:d,y:k,w:g,d:l,D:T,h:c,m:f,s,ms:i,Q:I}[m]||String(m||"").toLowerCase().replace(/s$/,"")},u:function(m){return m===void 0}},u="en",H={};H[u]=O;var z="$isDayjsObject",Z=function(m){return m instanceof U||!(!m||!m[z])},M=function m(v,p,w){var y;if(!v)return u;if(typeof v=="string"){var A=v.toLowerCase();H[A]&&(y=A),p&&(H[A]=p,y=A);var D=v.split("-");if(!y&&D.length>1)return m(D[0])}else{var W=v.name;H[W]=v,y=W}return!w&&y&&(u=y),y||!w&&u},$=function(m,v){if(Z(m))return m.clone();var p=typeof v=="object"?v:{};return p.date=m,p.args=arguments,new U(p)},L=R;L.l=M,L.i=Z,L.w=function(m,v){return $(m,{locale:v.$L,utc:v.$u,x:v.$x,$offset:v.$offset})};var U=function(){function m(p){this.$L=M(p.locale,null,!0),this.parse(p),this.$x=this.$x||p.x||{},this[z]=!0}var v=m.prototype;return v.parse=function(p){this.$d=function(w){var y=w.date,A=w.utc;if(y===null)return new Date(NaN);if(L.u(y))return new Date;if(y instanceof Date)return new Date(y);if(typeof y=="string"&&!/Z$/i.test(y)){var D=y.match(N);if(D){var W=D[2]-1||0,ee=(D[7]||"0").substring(0,3);return A?new Date(Date.UTC(D[1],W,D[3]||1,D[4]||0,D[5]||0,D[6]||0,ee)):new Date(D[1],W,D[3]||1,D[4]||0,D[5]||0,D[6]||0,ee)}}return new Date(y)}(p),this.init()},v.init=function(){var p=this.$d;this.$y=p.getFullYear(),this.$M=p.getMonth(),this.$D=p.getDate(),this.$W=p.getDay(),this.$H=p.getHours(),this.$m=p.getMinutes(),this.$s=p.getSeconds(),this.$ms=p.getMilliseconds()},v.$utils=function(){return L},v.isValid=function(){return this.$d.toString()!==S},v.isSame=function(p,w){var y=$(p);return this.startOf(w)<=y&&y<=this.endOf(w)},v.isAfter=function(p,w){return $(p)<this.startOf(w)},v.isBefore=function(p,w){return this.endOf(w)<$(p)},v.$g=function(p,w,y){return L.u(p)?this[w]:this.set(y,p)},v.unix=function(){return Math.floor(this.valueOf()/1e3)},v.valueOf=function(){return this.$d.getTime()},v.startOf=function(p,w){var y=this,A=!!L.u(w)||w,D=L.p(p),W=function(pe,ae){var be=L.w(y.$u?Date.UTC(y.$y,ae,pe):new Date(y.$y,ae,pe),y);return A?be:be.endOf(l)},ee=function(pe,ae){return L.w(y.toDate()[pe].apply(y.toDate("s"),(A?[0,0,0,0]:[23,59,59,999]).slice(ae)),y)},te=this.$W,ie=this.$M,ue=this.$D,ge="set"+(this.$u?"UTC":"");switch(D){case k:return A?W(1,0):W(31,11);case d:return A?W(1,ie):W(0,ie+1);case g:var ve=this.$locale().weekStart||0,ye=(te<ve?te+7:te)-ve;return W(A?ue-ye:ue+(6-ye),ie);case l:case T:return ee(ge+"Hours",0);case c:return ee(ge+"Minutes",1);case f:return ee(ge+"Seconds",2);case s:return ee(ge+"Milliseconds",3);default:return this.clone()}},v.endOf=function(p){return this.startOf(p,!1)},v.$set=function(p,w){var y,A=L.p(p),D="set"+(this.$u?"UTC":""),W=(y={},y[l]=D+"Date",y[T]=D+"Date",y[d]=D+"Month",y[k]=D+"FullYear",y[c]=D+"Hours",y[f]=D+"Minutes",y[s]=D+"Seconds",y[i]=D+"Milliseconds",y)[A],ee=A===l?this.$D+(w-this.$W):w;if(A===d||A===k){var te=this.clone().set(T,1);te.$d[W](ee),te.init(),this.$d=te.set(T,Math.min(this.$D,te.daysInMonth())).$d}else W&&this.$d[W](ee);return this.init(),this},v.set=function(p,w){return this.clone().$set(p,w)},v.get=function(p){return this[L.p(p)]()},v.add=function(p,w){var y,A=this;p=Number(p);var D=L.p(w),W=function(ie){var ue=$(A);return L.w(ue.date(ue.date()+Math.round(ie*p)),A)};if(D===d)return this.set(d,this.$M+p);if(D===k)return this.set(k,this.$y+p);if(D===l)return W(1);if(D===g)return W(7);var ee=(y={},y[f]=n,y[c]=o,y[s]=r,y)[D]||1,te=this.$d.getTime()+p*ee;return L.w(te,this)},v.subtract=function(p,w){return this.add(-1*p,w)},v.format=function(p){var w=this,y=this.$locale();if(!this.isValid())return y.invalidDate||S;var A=p||"YYYY-MM-DDTHH:mm:ssZ",D=L.z(this),W=this.$H,ee=this.$m,te=this.$M,ie=y.weekdays,ue=y.months,ge=y.meridiem,ve=function(ae,be,x,Oe){return ae&&(ae[be]||ae(w,A))||x[be].slice(0,Oe)},ye=function(ae){return L.s(W%12||12,ae,"0")},pe=ge||function(ae,be,x){var Oe=ae<12?"AM":"PM";return x?Oe.toLowerCase():Oe};return A.replace(E,function(ae,be){return be||function(x){switch(x){case"YY":return String(w.$y).slice(-2);case"YYYY":return L.s(w.$y,4,"0");case"M":return te+1;case"MM":return L.s(te+1,2,"0");case"MMM":return ve(y.monthsShort,te,ue,3);case"MMMM":return ve(ue,te);case"D":return w.$D;case"DD":return L.s(w.$D,2,"0");case"d":return String(w.$W);case"dd":return ve(y.weekdaysMin,w.$W,ie,2);case"ddd":return ve(y.weekdaysShort,w.$W,ie,3);case"dddd":return ie[w.$W];case"H":return String(W);case"HH":return L.s(W,2,"0");case"h":return ye(1);case"hh":return ye(2);case"a":return pe(W,ee,!0);case"A":return pe(W,ee,!1);case"m":return String(ee);case"mm":return L.s(ee,2,"0");case"s":return String(w.$s);case"ss":return L.s(w.$s,2,"0");case"SSS":return L.s(w.$ms,3,"0");case"Z":return D}return null}(ae)||D.replace(":","")})},v.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},v.diff=function(p,w,y){var A,D=this,W=L.p(w),ee=$(p),te=(ee.utcOffset()-this.utcOffset())*n,ie=this-ee,ue=function(){return L.m(D,ee)};switch(W){case k:A=ue()/12;break;case d:A=ue();break;case I:A=ue()/3;break;case g:A=(ie-te)/6048e5;break;case l:A=(ie-te)/864e5;break;case c:A=ie/o;break;case f:A=ie/n;break;case s:A=ie/r;break;default:A=ie}return y?A:L.a(A)},v.daysInMonth=function(){return this.endOf(d).$D},v.$locale=function(){return H[this.$L]},v.locale=function(p,w){if(!p)return this.$L;var y=this.clone(),A=M(p,w,!0);return A&&(y.$L=A),y},v.clone=function(){return L.w(this.$d,this)},v.toDate=function(){return new Date(this.valueOf())},v.toJSON=function(){return this.isValid()?this.toISOString():null},v.toISOString=function(){return this.$d.toISOString()},v.toString=function(){return this.$d.toUTCString()},m}(),B=U.prototype;return $.prototype=B,[["$ms",i],["$s",s],["$m",f],["$H",c],["$W",l],["$M",d],["$y",k],["$D",T]].forEach(function(m){B[m[1]]=function(v){return this.$g(v,m[0],m[1])}}),$.extend=function(m,v){return m.$i||(m(v,U,$),m.$i=!0),$},$.locale=M,$.isDayjs=Z,$.unix=function(m){return $(1e3*m)},$.en=H[u],$.Ls=H,$.p={},$})})(br);var Aa=br.exports;const Er=xt(Aa);var Xe=function(){return Xe=Object.assign||function(t){for(var r,n=1,o=arguments.length;n<o;n++){r=arguments[n];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t},Xe.apply(this,arguments)};function Ze(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}function Ve(e,t,r){if(r||arguments.length===2)for(var n=0,o=t.length,i;n<o;n++)(i||!(n in t))&&(i||(i=Array.prototype.slice.call(t,0,n)),i[n]=t[n]);return e.concat(i||Array.prototype.slice.call(t))}typeof SuppressedError=="function"&&SuppressedError;function ze(e,t){var r=t&&t.cache?t.cache:Ha,n=t&&t.serializer?t.serializer:Fa,o=t&&t.strategy?t.strategy:ka;return o(e,{cache:r,serializer:n})}function ja(e){return e==null||typeof e=="number"||typeof e=="boolean"}function Ia(e,t,r,n){var o=ja(n)?n:r(n),i=t.get(o);return typeof i>"u"&&(i=e.call(this,n),t.set(o,i)),i}function Cr(e,t,r){var n=Array.prototype.slice.call(arguments,3),o=r(n),i=t.get(o);return typeof i>"u"&&(i=e.apply(this,n),t.set(o,i)),i}function Or(e,t,r,n,o){return r.bind(t,e,n,o)}function ka(e,t){var r=e.length===1?Ia:Cr;return Or(e,this,r,t.cache.create(),t.serializer)}function La(e,t){return Or(e,this,Cr,t.cache.create(),t.serializer)}var Fa=function(){return JSON.stringify(arguments)};function ht(){this.cache=Object.create(null)}ht.prototype.get=function(e){return this.cache[e]},ht.prototype.set=function(e,t){this.cache[e]=t};var Ha={create:function(){return new ht}},Ye={variadic:La};function Va(e,t,r){if(r===void 0&&(r=Error),!e)throw new r(t)}ze(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.NumberFormat).bind.apply(e,Ve([void 0],t,!1)))},{strategy:Ye.variadic}),ze(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.DateTimeFormat).bind.apply(e,Ve([void 0],t,!1)))},{strategy:Ye.variadic}),ze(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.PluralRules).bind.apply(e,Ve([void 0],t,!1)))},{strategy:Ye.variadic}),ze(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.Locale).bind.apply(e,Ve([void 0],t,!1)))},{strategy:Ye.variadic}),ze(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.ListFormat).bind.apply(e,Ve([void 0],t,!1)))},{strategy:Ye.variadic});var za=function(e){process.env.NODE_ENV!=="production"&&console.error(e)},Ya=function(e){process.env.NODE_ENV!=="production"&&console.warn(e)},Ba={formats:{},messages:{},timeZone:void 0,defaultLocale:"en",defaultFormats:{},fallbackOnEmptyString:!0,onError:za,onWarn:Ya};function Ua(e){Va(e,"[React Intl] Could not find required `intl` object. <IntlProvider> needs to exist in the component ancestry.")}Xe(Xe({},Ba),{textComponent:P.Fragment});function _r(e,t){if(e===t)return!0;if(!e||!t)return!1;var r=Object.keys(e),n=Object.keys(t),o=r.length;if(n.length!==o)return!1;for(var i=0;i<o;i++){var s=r[i];if(e[s]!==t[s]||!Object.prototype.hasOwnProperty.call(t,s))return!1}return!0}var mt={exports:{}},G={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Tr;function Wa(){if(Tr)return G;Tr=1;var e=typeof Symbol=="function"&&Symbol.for,t=e?Symbol.for("react.element"):60103,r=e?Symbol.for("react.portal"):60106,n=e?Symbol.for("react.fragment"):60107,o=e?Symbol.for("react.strict_mode"):60108,i=e?Symbol.for("react.profiler"):60114,s=e?Symbol.for("react.provider"):60109,f=e?Symbol.for("react.context"):60110,c=e?Symbol.for("react.async_mode"):60111,l=e?Symbol.for("react.concurrent_mode"):60111,g=e?Symbol.for("react.forward_ref"):60112,d=e?Symbol.for("react.suspense"):60113,I=e?Symbol.for("react.suspense_list"):60120,k=e?Symbol.for("react.memo"):60115,T=e?Symbol.for("react.lazy"):60116,S=e?Symbol.for("react.block"):60121,N=e?Symbol.for("react.fundamental"):60117,E=e?Symbol.for("react.responder"):60118,O=e?Symbol.for("react.scope"):60119;function b(u){if(typeof u=="object"&&u!==null){var H=u.$$typeof;switch(H){case t:switch(u=u.type,u){case c:case l:case n:case i:case o:case d:return u;default:switch(u=u&&u.$$typeof,u){case f:case g:case T:case k:case s:return u;default:return H}}case r:return H}}}function R(u){return b(u)===l}return G.AsyncMode=c,G.ConcurrentMode=l,G.ContextConsumer=f,G.ContextProvider=s,G.Element=t,G.ForwardRef=g,G.Fragment=n,G.Lazy=T,G.Memo=k,G.Portal=r,G.Profiler=i,G.StrictMode=o,G.Suspense=d,G.isAsyncMode=function(u){return R(u)||b(u)===c},G.isConcurrentMode=R,G.isContextConsumer=function(u){return b(u)===f},G.isContextProvider=function(u){return b(u)===s},G.isElement=function(u){return typeof u=="object"&&u!==null&&u.$$typeof===t},G.isForwardRef=function(u){return b(u)===g},G.isFragment=function(u){return b(u)===n},G.isLazy=function(u){return b(u)===T},G.isMemo=function(u){return b(u)===k},G.isPortal=function(u){return b(u)===r},G.isProfiler=function(u){return b(u)===i},G.isStrictMode=function(u){return b(u)===o},G.isSuspense=function(u){return b(u)===d},G.isValidElementType=function(u){return typeof u=="string"||typeof u=="function"||u===n||u===l||u===i||u===o||u===d||u===I||typeof u=="object"&&u!==null&&(u.$$typeof===T||u.$$typeof===k||u.$$typeof===s||u.$$typeof===f||u.$$typeof===g||u.$$typeof===N||u.$$typeof===E||u.$$typeof===O||u.$$typeof===S)},G.typeOf=b,G}var K={};/** @license React v16.13.1
 * react-is.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sr;function qa(){return Sr||(Sr=1,process.env.NODE_ENV!=="production"&&function(){var e=typeof Symbol=="function"&&Symbol.for,t=e?Symbol.for("react.element"):60103,r=e?Symbol.for("react.portal"):60106,n=e?Symbol.for("react.fragment"):60107,o=e?Symbol.for("react.strict_mode"):60108,i=e?Symbol.for("react.profiler"):60114,s=e?Symbol.for("react.provider"):60109,f=e?Symbol.for("react.context"):60110,c=e?Symbol.for("react.async_mode"):60111,l=e?Symbol.for("react.concurrent_mode"):60111,g=e?Symbol.for("react.forward_ref"):60112,d=e?Symbol.for("react.suspense"):60113,I=e?Symbol.for("react.suspense_list"):60120,k=e?Symbol.for("react.memo"):60115,T=e?Symbol.for("react.lazy"):60116,S=e?Symbol.for("react.block"):60121,N=e?Symbol.for("react.fundamental"):60117,E=e?Symbol.for("react.responder"):60118,O=e?Symbol.for("react.scope"):60119;function b(x){return typeof x=="string"||typeof x=="function"||x===n||x===l||x===i||x===o||x===d||x===I||typeof x=="object"&&x!==null&&(x.$$typeof===T||x.$$typeof===k||x.$$typeof===s||x.$$typeof===f||x.$$typeof===g||x.$$typeof===N||x.$$typeof===E||x.$$typeof===O||x.$$typeof===S)}function R(x){if(typeof x=="object"&&x!==null){var Oe=x.$$typeof;switch(Oe){case t:var Ae=x.type;switch(Ae){case c:case l:case n:case i:case o:case d:return Ae;default:var Me=Ae&&Ae.$$typeof;switch(Me){case f:case g:case T:case k:case s:return Me;default:return Oe}}case r:return Oe}}}var u=c,H=l,z=f,Z=s,M=t,$=g,L=n,U=T,B=k,m=r,v=i,p=o,w=d,y=!1;function A(x){return y||(y=!0,console.warn("The ReactIs.isAsyncMode() alias has been deprecated, and will be removed in React 17+. Update your code to use ReactIs.isConcurrentMode() instead. It has the exact same API.")),D(x)||R(x)===c}function D(x){return R(x)===l}function W(x){return R(x)===f}function ee(x){return R(x)===s}function te(x){return typeof x=="object"&&x!==null&&x.$$typeof===t}function ie(x){return R(x)===g}function ue(x){return R(x)===n}function ge(x){return R(x)===T}function ve(x){return R(x)===k}function ye(x){return R(x)===r}function pe(x){return R(x)===i}function ae(x){return R(x)===o}function be(x){return R(x)===d}K.AsyncMode=u,K.ConcurrentMode=H,K.ContextConsumer=z,K.ContextProvider=Z,K.Element=M,K.ForwardRef=$,K.Fragment=L,K.Lazy=U,K.Memo=B,K.Portal=m,K.Profiler=v,K.StrictMode=p,K.Suspense=w,K.isAsyncMode=A,K.isConcurrentMode=D,K.isContextConsumer=W,K.isContextProvider=ee,K.isElement=te,K.isForwardRef=ie,K.isFragment=ue,K.isLazy=ge,K.isMemo=ve,K.isPortal=ye,K.isProfiler=pe,K.isStrictMode=ae,K.isSuspense=be,K.isValidElementType=b,K.typeOf=R}()),K}process.env.NODE_ENV==="production"?mt.exports=Wa():mt.exports=qa();var Ga=mt.exports,wr=Ga,Ka={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},Ja={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},xr={};xr[wr.ForwardRef]=Ka,xr[wr.Memo]=Ja;var vt=typeof window<"u"&&!window.__REACT_INTL_BYPASS_GLOBAL_CONTEXT__?window.__REACT_INTL_CONTEXT__||(window.__REACT_INTL_CONTEXT__=P.createContext(null)):P.createContext(null);vt.Consumer,vt.Provider;var Xa=vt;function Qe(){var e=P.useContext(Xa);return Ua(e),e}var pt;(function(e){e.formatDate="FormattedDate",e.formatTime="FormattedTime",e.formatNumber="FormattedNumber",e.formatList="FormattedList",e.formatDisplayName="FormattedDisplayName"})(pt||(pt={}));var gt;(function(e){e.formatDate="FormattedDateParts",e.formatTime="FormattedTimeParts",e.formatNumber="FormattedNumberParts",e.formatList="FormattedListParts"})(gt||(gt={}));function Mr(e){var t=function(r){var n=Qe(),o=r.value,i=r.children,s=Ze(r,["value","children"]),f=typeof o=="string"?new Date(o||0):o,c=e==="formatDate"?n.formatDateToParts(f,s):n.formatTimeToParts(f,s);return i(c)};return t.displayName=gt[e],t}function Be(e){var t=function(r){var n=Qe(),o=r.value,i=r.children,s=Ze(r,["value","children"]),f=n[e](o,s);if(typeof i=="function")return i(f);var c=n.textComponent||P.Fragment;return P.createElement(c,null,f)};return t.displayName=pt[e],t}function Za(e,t){var r=e.values,n=Ze(e,["values"]),o=t.values,i=Ze(t,["values"]);return _r(o,r)&&_r(n,i)}function Rr(e){var t=Qe(),r=t.formatMessage,n=t.textComponent,o=n===void 0?P.Fragment:n,i=e.id,s=e.description,f=e.defaultMessage,c=e.values,l=e.children,g=e.tagName,d=g===void 0?o:g,I=e.ignoreTag,k={id:i,description:s,defaultMessage:f},T=r(k,c,{ignoreTag:I});return typeof l=="function"?l(Array.isArray(T)?T:[T]):d?P.createElement(d,null,P.Children.toArray(T)):P.createElement(P.Fragment,null,T)}Rr.displayName="FormattedMessage";var yt=P.memo(Rr,Za);yt.displayName="MemoizedFormattedMessage",Be("formatDate"),Be("formatTime"),Be("formatNumber"),Be("formatList"),Be("formatDisplayName"),Mr("formatDate"),Mr("formatTime");const Qa=(e,t=2)=>{if(isNaN(e))return 0;let r=Math.floor(e).toString(),n=Math.round(e%1*100).toString().padEnd(t,"0"),o=[];for(;r.length>3;)o.unshift(r.slice(-3)),r=r.slice(0,-3);o.unshift(r);let i=o.join(",");return n?`${i}.${n}`:i},Pr=localStorage.getItem(Ge.THEME)||"";function $r(e=1){let t=100;switch(e){case 1:t=100;break;case 2:t=116;break;case 3:t=136;break;case 4:t=168;break;case 5:t=232;break;case 6:t=280;break}return t}const xe=(e,t)=>{const r=e?`${e}.${t}`:t;return C.jsx(yt,{id:r})},Nr=(e,t,r="",n=!0)=>{const o=fe.get(fe.find(t,{key:e}),"value","");return o?n?C.jsxs(C.Fragment,{children:[e&&C.jsxs("span",{children:[e," - "]}),xe(r,o)]}):xe(r,o):e},Dr=(e,t)=>{const{valueType:r,dictType:n,data:o,prefix:i,showKey:s,amountLength:f,optionPrefix:c}=t;switch(r){case Pe.Amount:return Qa(e,f);case Pe.Dictionary:return Nr(e,[],i,s);case Pe.MockDictionary:return Nr(e,o,c||i,s);case Pe.DateTime:return e&&Er(e).format(cn);case Pe.Date:return e&&Er(e).format(sn);default:return e}},eo=e=>{let{valueType:t,render:r}=e;const n={...e};switch(t){case Pe.Ellipsis:n.ellipsis=!0;break;default:fe.isNil(r)&&(n.render=o=>Dr(o,e));break}return delete n.data,n},Ar={copy:C.jsx(sr,{}),detail:C.jsx(ur,{}),edit:C.jsx(ft,{}),delete:C.jsx(cr,{}),cancel:C.jsx(or,{}),save:C.jsx(pr,{}),up:C.jsx(ar,{}),down:C.jsx(nr,{}),search:C.jsx(dt,{}),flowNode:C.jsx(rr,{}),dispatch:C.jsx(gr,{}),dowload:C.jsx(lr,{}),aiHelp:C.jsx(ir,{}),menuAuth:C.jsx(hr,{}),userDetail:C.jsx(dr,{}),fieldManage:C.jsx(fr,{})},Te=e=>{const{type:t,disabled:r=!1,loading:n=!1,onClick:o=()=>{}}=e,i=xe(_e.COMMON,t);switch(t){case X.copy:case X.detail:case X.edit:case X.cancel:case X.save:case X.up:case X.down:case X.flowNode:case X.dispatch:case X.dowload:case X.aiHelp:case X.menuAuth:case X.userDetail:case X.fieldManage:return C.jsx(F.Tooltip,{placement:"top",title:i,children:C.jsx(F.Button,{type:"link",style:{color:Pr},icon:Ar[t],disabled:r,loading:n,onClick:()=>o(t)})},t);case X.delete:return C.jsx(F.Tooltip,{placement:"top",title:i,children:C.jsx(F.Popconfirm,{title:xe(_e.COMMON,"confirmDelete"),okText:xe(_e.COMMON,"confirm"),cancelText:xe(_e.COMMON,"cancel"),onConfirm:()=>o(t),children:C.jsx(F.Button,{type:"link",icon:Ar[t],disabled:r,danger:!0,loading:n})})},t);default:return C.jsx(F.Tooltip,{title:xe(_e.COMMON,"edit"),children:C.jsx(F.Button,{type:"link",style:{color:Pr},icon:C.jsx(ft,{}),disabled:r,loading:n,onClick:()=>o(t)})})}},to=e=>{switch(localStorage.getItem("locale")){case ln.EN.locale:return`Toal ${e}`;default:return`共 ${e} 条`}},ro=()=>{const e={permissionButtons:[]};return{hasPermission:r=>{for(const n of e.permissionButtons)if(r===n.permissionCode)return!0;return!0}}},no=localStorage.getItem(Ge.THEME)||"",Ue=()=>{const e=Qe(),{hasPermission:t}=ro(),r=(T,S,N)=>{const E=`${T}.${S}`;let O=e.formatMessage({id:E})||"";return N&&Object.entries(N).forEach(([b,R])=>{O=O.replace(`{${b}}`,String(R))}),O},n=(T,S,N,E)=>{F.notification[S]({message:r("common","tip"),duration:E||3,description:r(T,N)})},o=(T,S,N="")=>{const E=X[T]&&T!==X.list?r("common",X[T]):"",O=N?r(S,N):"";return`${E}${O}`},i=(T,S,N,E)=>{let O=E?r("common","success"):r("common","fail");return o(N,S,T)+O},s=(T,S)=>{const N=T?`${T}.${S}`:S;return C.jsx(yt,{id:N})},f=(T,S)=>e.formatDate(T,S),c=(T,S)=>e.formatNumber(T,S),l=(T,S=!0,N="")=>{const E=O=>O.map(b=>{var H;const R=N?r(N,b.value):b.value,u={...b,key:b.key,value:b.key,label:S&&((H=b.key)!=null&&H.trim())?`${b.key} - ${R}`:R};return b.children&&b.children.length>0&&(u.children=E(b.children)),u});return T&&T.length>0?E(T):[]},g=(T,S=!0,N="")=>{const E={};return T.forEach(O=>{const b=N?r(N,O.value):O.value;E[O.key]={text:S&&O.key?`${O.key} - ${b}`:b}}),E},d=(T=[],S=N=>{})=>{if(T&&T.length>0){const N=[];return T.forEach(E=>{let O,b,R=!1,u,H;typeof E=="object"?(O=E.type,b=E.title||E.type,b=E.prefix&&b?r(E.prefix,b):b,R=(E==null?void 0:E.disabled)||!1,u=E==null?void 0:E.icon,H=E.permissionId):typeof E=="string"&&(O=E),!(H&&!t(H))&&(u?N.push(C.jsx(F.Tooltip,{placement:"top",title:b,children:C.jsx(F.Button,{type:"link",icon:u,disabled:R??!1,style:{color:no},onClick:()=>S(E)})},O)):O&&N.push(Te({type:O,disabled:R,onClick:S})))}),C.jsx(F.Space,{children:N})}else return null},I=r("common","inputPlaceholder"),k=r("common","selectPlaceholder");return{translate:r,formatActionTitle:o,formateHtmlText:s,formatLocalDate:f,formatLocalNumber:c,getSelectOption:l,getEditTableSelectOption:g,getActionResultTitle:i,openNotificationTip:n,defaultInputPlaceholder:I,defaultSelectPlaceholder:k,renderActionColumn:d}},ao=({descriptionsItems:e,bordered:t,size:r,prefix:n,num:o})=>{const{translate:i}=Ue(),s=()=>{let f=[];for(let c in e){const l=typeof e[c];if(e[c]&&l==="object"){const d=e[c];let I="",k=fe.isNil(d==null?void 0:d.prefixKey)?n:d.prefixKey;fe.isNil(d==null?void 0:d.context)||(I=Dr(d.context,{...d,prefix:k}));const T=k?i(k,c):c,S=fe.isNil(d.props)?{}:{...d.props};f.push({key:c,label:T,children:I,...S})}else f.push({key:c,label:i(n,c),children:e[c]})}return f};return C.jsx(F.Descriptions,{bordered:t,size:r,column:o||3,items:s(),style:{width:"380px"}})},bt=".5rem",De={mls:{marginLeft:bt},mtbs:{marginTop:bt,marginBottom:bt}},jr={green:"#00994e",blue:"#345da7"},oo=localStorage.getItem(Ge.THEME)||"",Ir=({children:e,color:t=oo||jr.green,type:r,size:n,icon:o,style:i,disabled:s=!1,loading:f=!1,onClick:c=()=>{}})=>C.jsx(F.ConfigProvider,{theme:{components:{Button:{colorPrimary:t}}},children:C.jsx(F.Button,{type:r||"primary",size:n||"middle",disabled:s,loading:f,icon:o,style:i,onClick:fe.debounce(c),children:e})}),We=160,Et={marginRight:"2rem"},io=localStorage.getItem(Ge.THEME)||jr.blue,so=({children:e,searchTitle:t="",resetTitle:r="",createTitle:n="",searchValue:o={},resetValue:i={},searchSource:s=[],intlPrefix:f="",buttonDisabled:c=!1,onSearch:l=k=>{},onCreate:g=()=>{},onChange:d,onRest:I})=>{const{translate:k,getSelectOption:T,defaultInputPlaceholder:S,defaultSelectPlaceholder:N}=Ue(),[E,O]=q.useState(o),[b]=F.Form.useForm(),R=M=>{const $={rules:M.rules,name:M.value,label:void 0};if(M.label){const L=M.prefix||f;$.label=L?k(L,M.label):M.label}return $},u=M=>{const $={allowClear:!0,style:{}},L=Object.values(dn);for(const v in M)L.includes(v)||($[v]=M[v]);const{type:U,width:B}=M;let m=null;switch(U){case Se.INPUT:return $.style={width:B||We},m=C.jsx(F.Input,{placeholder:S,...$}),m;case Se.CHECK_BOX:return m=C.jsx(F.Checkbox.Group,{options:M.data}),m;case Se.SELECT:{$.style={width:B||We};const v=M.prefix||f;let p=!(M.isint&&M.isint==="0"),w=[];return fe.isNil(M.dictType)?w=M.data&&M.bind?M.data.filter(y=>y.bind===E[M.bind]||y.key===""):M.data:p=!1,m=C.jsx(F.Select,{placeholder:N,...$,options:p?T(w,M.showKey,v):w}),m}case Se.TREE_SELECT:return $.style={width:B||We},m=C.jsx(F.TreeSelect,{...$,treeData:M.data}),m;case Se.CASCADER:return $.style={width:B||We},m=C.jsx(F.Cascader,{placeholder:N,...$,options:M.data}),m;case Se.DATE_PICKER:return $.style={width:B||We},m=C.jsx(F.DatePicker,{...$}),m;case Se.RANGE_PICKER:return $.style={width:B||240},m=C.jsx(F.DatePicker.RangePicker,{...$}),m;default:return C.jsx(F.Input,{...$})}},H=(M,$)=>{d&&d(M,$);let L=Object.keys(M)[0],U="";s.forEach(B=>{B.value===L&&B.ref&&(U=B.ref)}),U?(O({...$,[U]:null}),b.setFieldsValue({[U]:null})):O($)},z=()=>{b.setFieldsValue(i),I&&I(o),Z()},Z=()=>{b.validateFields().then(M=>{l(M)}).catch(M=>{M.errorFields&&b.setFields(M.errorFields)})};return C.jsx("div",{className:"search-box",children:C.jsxs(F.Form,{layout:"inline",colon:!1,form:b,initialValues:o,onValuesChange:fe.debounce(H),children:[s.map(M=>C.jsx(F.Form.Item,{...R(M),style:{...De.mtbs,...Et},children:u(M)},M.value)),C.jsx(F.Button,{type:"primary",icon:C.jsx(dt,{}),disabled:c,style:De.mtbs,onClick:fe.debounce(()=>Z()),children:t}),C.jsx(F.Button,{icon:C.jsx(vr,{}),disabled:c,style:{...De.mtbs,...De.mls,...Et},onClick:fe.debounce(z),children:r}),n&&C.jsx(Ir,{color:io,type:"primary",size:"middle",icon:C.jsx(mr,{}),disabled:c,style:{...De.mtbs,...Et},onClick:g,children:n}),e&&C.jsx("div",{style:De.mtbs,children:e})]})})},co={commonParamTable:"_commonParamTable_1ilux_10"},lo={currentPage:1,defaultPageSize:50,pageSize:50},uo=(e,t,r,n)=>e.length?[{title:t(_e.COMMON,"option"),dataIndex:"option",key:"option",align:"center",fixed:"right",width:$r(e.length-2),render:(o,i)=>r(e,s=>n(s,i,e))}]:[],fo=({rowKey:e="id",columns:t=[],optionList:r=[],paginationConfig:n,dataSource:o=[],loading:i=!1,intlPrefix:s,components:f,props:c={},onAction:l,onChange:g})=>{const{translate:d,renderActionColumn:I}=Ue(),[k,T]=q.useState([]),S=q.useCallback(l||(()=>{}),[l]),N=q.useCallback(g||(()=>{}),[g]);q.useEffect(()=>{Array.isArray(o)?T(o):T([])},[o]);const E=q.useCallback(R=>R.map(u=>{u={width:120,align:"center",ellipsis:!0,...u};let{title:H,key:z,prefix:Z,children:M}=u;u.prefix=Z||s,H=d(u.prefix,H);const $=eo(u);return delete $.valueType,Array.isArray(M)&&($.children=E(M)),{...$,title:H}}),[s,d]),O=q.useMemo(()=>{const R=E(t),u=uo(r,d,I,S);return[...R,...u]},[t,r,d,I,S,E]),b=q.useMemo(()=>typeof n=="object"?{...lo,...n,showTotal:to}:n,[n]);return C.jsx(C.Fragment,{children:C.jsx(F.Table,{className:co.commonParamTable,tableLayout:"fixed",rowKey:e,components:f,dataSource:k,columns:O,loading:i,pagination:b,scroll:{x:"max-content",y:2e3},onChange:N,...c})})},ho=({intlPrefix:e,rowKey:t,optionCount:r,orderFiled:n,editableKeys:o,tableData:i,setTableData:s,getOptionList:f,onAction:c})=>{const{translate:l,getEditTableSelectOption:g,openNotificationTip:d}=Ue(),I=(S,N,E)=>f(S).map(b=>{const{optionType:R,disabled:u=!1,loading:H=!1}=b,z={type:R,disabled:u,loading:H};switch(R){case X.edit:return Te({...z,onClick:()=>{var Z;(Z=E==null?void 0:E.startEditable)==null||Z.call(E,S[t]),c(R,S)}});case X.detail:return Te({...z,onClick:()=>c(R,S)});case X.delete:return Te({...z,onClick:()=>{s(Z=>Z.filter(M=>M[t]!==S[t])),c(R,S)}});case X.copy:return Te({...z,onClick:()=>{var M;const Z=JSON.parse(JSON.stringify(i));Z.push({...S,[`${t}`]:Date.now(),isAdd:!0}),s(Z),(M=E==null?void 0:E.startEditable)==null||M.call(E,Z.at(-1)[t]),c(R,S)}});case X.up:case X.down:return Te({...z,onClick:()=>{T(N,R),c(R,S)}});default:return C.jsx(C.Fragment,{})}}),k=S=>{const N=S.map(E=>{const O={...E},{prefix:b=e,title:R,valueType:u,data:H,showKey:z=!1,optionPrefix:Z}=E,M=l(b,R);let $={};return u===un.SELECT&&!fe.isNil(H)&&($={valueEnum:g(H,z,fe.isNil(Z)?b:Z)},delete O.data,delete O.showKey),{...O,...$,title:M}});return r&&N.push({title:l(_e.COMMON,"option"),valueType:"option",align:"center",width:$r(r),render:(E,O,b,R)=>I(O,b,R)}),N},T=(S,N)=>{if(o.length){d("common",rt.WARNING,"formEditing");return}if(N===X.up&&S===0){d("common",rt.WARNING,"firstRow");return}if(N===X.down&&S===i.length-1){d("common",rt.WARNING,"lastRow");return}let E=N===X.up?S-1:S+1;s(O=>{const b={...O[S]},R={...O[E]},u=n?{[n]:R==null?void 0:R[n]}:{},H=n?{[n]:b==null?void 0:b[n]}:{},z=[...O],[Z,M]=[z[S],z[E]];return z[S]={...M,...H},z[E]={...Z,...u},z})};return{getColumns:k}},mo=q.forwardRef(({rowKey:e,columns:t,dataSource:r,intlPrefix:n,canEdit:o,editableType:i="single",showCreate:s=!0,optionCount:f=0,loading:c=!1,orderFiled:l,editTableType:g,getOptionList:d=()=>[],afterMount:I=()=>{},onCreate:k=O=>({id:O+1}),onAction:T=(O,b)=>{},onFormChange:S=(O,b,R)=>{},onFormSave:N=(O,b,R)=>{}},E=null)=>{const{translate:O}=Ue(),[b,R]=q.useState([]),[u,H]=q.useState([]),z=q.useRef(),Z=q.useRef(null),[M]=F.Form.useForm(),{getColumns:$}=ho({intlPrefix:n,rowKey:e,optionCount:f,orderFiled:l,editableKeys:b,tableData:u,setTableData:H,getOptionList:d,onAction:T});q.useImperativeHandle(E,()=>({async validateFields(){var U;try{return i==="single"&&b.length?{errorCode:fn.EDITING}:(await((U=z==null?void 0:z.current)==null?void 0:U.validateFields()),u)}catch{return null}},resetFields(){Array.isArray(b)&&(R([]),M.resetFields()),H(r)},getFieldsValue(){var U;return(U=z==null?void 0:z.current)==null?void 0:U.getFieldsValue()},setRowData(U,B){var m,v;(v=(m=z.current)==null?void 0:m.setRowData)==null||v.call(m,U,{...B})},getRowData(U){var B,m;return((B=z.current)==null?void 0:B.getRowData)&&((m=z.current)==null?void 0:m.getRowData(U))},setCustomEditableKeys(U){R(U)},getEditableKeys(){return b}})),q.useEffect(()=>{H(Array.isArray(r)?r:[])},[r]),q.useEffect(()=>{I(M)},[]);const L={rowKey:e,loading:c,columns:$(t),value:u,recordCreatorProps:o&&s?{position:"bottom",record:k,creatorButtonText:O(_e.COMMON,"create")}:!1,editableFormRef:z,actionRef:Z,editable:{form:M,type:i,editableKeys:b,onChange:R,onSave:(U,B,m)=>N(U,B,m),actionRender:(U,B,m)=>[m.save,m.cancel],saveText:Te({type:X.save}),cancelText:Te({type:X.cancel})},onChange:H};return g==="caseDemo"&&delete L.onChange,C.jsx(wt.EditableProTable,{...L})}),vo=({action:e,maxSize:t=10,onSuccess:r,onError:n})=>{const[o,i]=q.useState(!1),s=l=>l.type==="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"||l.type==="application/vnd.ms-excel"||/\.xlsx?$/i.test(l.name)?l.size/1024/1024<=t?!0:(F.message.error(`文件大小不能超过 ${t}MB`),F.Upload.LIST_IGNORE):(F.message.error("只能上传 Excel 文件 (.xlsx, .xls)"),F.Upload.LIST_IGNORE),f=(l,g)=>{var I;const d=((I=l==null?void 0:l.response)==null?void 0:I.message)||(l==null?void 0:l.message)||"上传失败";F.message.error(`${g} ${d}`),n==null||n(new Error(d))},c={name:"file",action:e,accept:".xlsx, .xls",multiple:!1,showUploadList:!1,beforeUpload:s,headers:{"Content-Type":"multipart/form-data"},onChange(l){var g,d;if(l.file.status==="uploading"){i(!0);return}if(l.file.status==="done"){if(((d=(g=l.file.response)==null?void 0:g.header)==null?void 0:d.errorCode)!=="000000"){f(l.file.response,l.file.name),i(!1);return}F.message.success(`${l.file.name} 上传成功`),i(!1),r==null||r(l.file.originFileObj)}else l.file.status==="error"&&(f(l.file.response,l.file.name),i(!1))}};return C.jsx(F.Upload,{...c,children:C.jsx(F.Button,{style:{marginRight:6},icon:C.jsx(yr,{}),type:"primary",loading:o,children:"下载模板"})})};ce.Button=tn,ce.Card=nn,ce.CommonModal=on,ce.CommonTable=fo,ce.EditTable=mo,ce.GradientButton=Ir,ce.Search=so,ce.TabDescription=ao,ce.UploadCom=vo,Object.defineProperty(ce,Symbol.toStringTag,{value:"Module"})});
