declare const _default: {
    header: {
        requestId: string;
        gid: string;
        errorCode: string;
        errorMsg: string;
        success: boolean;
    };
    data: {
        resourceList: {
            id: number;
            displayOrder: number;
            identifierField: string;
            resourceName: string;
            resourceUrl: string;
            resourceDesc: string;
            createTime: string;
            updateTime: string;
            createUser: string;
            updateUser: string;
            children: ({
                parentId: number;
                id: number;
                displayOrder: number;
                identifierField: string;
                resourceName: string;
                resourceUrl: string;
                resourceDesc: string;
                createTime: string;
                updateTime: string;
                createUser: string;
                updateUser: string;
                permissions: {
                    permissionId: string;
                    permissionName: string;
                }[];
                children?: undefined;
            } | {
                parentId: number;
                id: number;
                displayOrder: number;
                identifierField: string;
                resourceName: string;
                resourceUrl: string;
                resourceDesc: string;
                createTime: string;
                updateTime: string;
                createUser: string;
                updateUser: string;
                children: {
                    parentId: number;
                    id: number;
                    displayOrder: number;
                    identifierField: string;
                    resourceName: string;
                    resourceUrl: string;
                    resourceDesc: string;
                    createTime: string;
                    updateTime: string;
                    createUser: string;
                    updateUser: string;
                    permissions: {
                        permissionId: string;
                        permissionName: string;
                    }[];
                }[];
                permissions?: undefined;
            })[];
        }[];
    };
};
export default _default;
