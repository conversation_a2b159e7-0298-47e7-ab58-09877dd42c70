declare const DICT_CONSTANTS: {
    DICT_ENUM_MAP: {
        crcdOrgNo: {
            type: string;
            url: string;
            optionKey: string;
            optionValue: string;
        };
        nodeAttriId: {
            type: string;
            url: string;
            optionKey: string;
            optionValue: string;
        };
        nodeRule: {
            type: string;
            url: string;
            optionKey: string;
            optionValue: string;
        };
        nodeRuleFactor: {
            type: string;
            url: string;
            optionKey: string;
            optionValue: string;
        };
        language: {
            type: string;
            url: string;
            optionKey: string;
            optionValue: string;
        };
        roleIds: {
            type: string;
            url: string;
            optionKey: string;
            optionValue: string;
        };
        positionIds: {
            type: string;
            url: string;
            optionKey: string;
            optionValue: string;
        };
    };
    PERMISSION_BUTTON: {
        key: string;
        value: string;
    }[];
    PAGEOPTIONS: {
        label: string;
        value: number;
    }[];
    PARAM_STATUS: {
        key: string;
        value: string;
    }[];
    LABEL_TYPE: {
        key: string;
        value: string;
    }[];
    LABEL_NAME_SRC: {
        key: string;
        value: string;
    }[];
    NODE_AUTH: {
        key: string;
        value: string;
    }[];
    NODE_TYPE: {
        key: string;
        value: string;
    }[];
    NODE_STATUS: {
        key: string;
        value: string;
    }[];
    NODE_ATTRI_TYPE: {
        key: string;
        value: string;
    }[];
    AUTH_PAGE: {
        key: string;
        value: string;
    }[];
    RIGHT_ACTION: {
        key: string;
        value: string;
        label: string;
    }[];
    LEFT_ACTION: {
        key: string;
        value: string;
        label: string;
    }[];
    CENTER_FILES: ({
        key: string;
        value: string;
        disabled: boolean;
    } | {
        key: string;
        value: string;
        disabled?: undefined;
    })[];
    RULE_TYPE: {
        key: string;
        value: string;
    }[];
    RULE_FACTORS_TYPE: {
        key: string;
        label: string;
        value: string;
    }[];
    RULE_OPTION_TYPE: {
        key: string;
        value: string;
    }[];
    CHARACTER: {
        key: string;
        value: string;
    }[];
    LABEL_ATTRIBUTE: {
        key: string;
        value: string;
    }[];
    LABEL_SOURCE: {
        key: string;
        value: string;
    }[];
    RULE_OPERATOR_TYPE: {
        key: string;
        value: string;
    }[];
    WORKBENCH_MODEL: {
        key: string;
        value: string;
    }[];
    CASEMANAGE_MODEL: {
        key: string;
        value: string;
    }[];
    WORKBENCH_CURR_STATE: {
        key: string;
        value: string;
    }[];
    WORKBENCH_CASE_STATE: {
        key: string;
        value: string;
    }[];
    STATION: {
        key: string;
        value: string;
    }[];
    GENDER: {
        key: string;
        value: string;
    }[];
    LANGUAGE: {
        key: string;
        value: string;
    }[];
    LEVEL: {
        key: string;
        value: string;
    }[];
    YES_NO: {
        key: string;
        value: string;
    }[];
    SEARCH_SOURCE_LIST: {
        key: string;
        value: string;
    }[];
    FIRST_USG_FLAG_LIST: {
        key: string;
        value: string;
        label: string;
    }[];
    SUPL_CARD_FLAG_LIST: {
        key: string;
        value: string;
    }[];
    CRCD_STS_LIST: {
        key: string;
        value: string;
    }[];
    CARD_TYPE_LIST: {
        key: string;
        value: string;
    }[];
    CARD_ANNUAL_FEE_FLAG_LIST: {
        key: string;
        value: string;
    }[];
    CCY_LIST: {
        key: string;
        value: string;
    }[];
    CARD_ACTIVE_STS_LSIT: {
        key: string;
        value: string;
    }[];
    CARD_TRX_RANGE_FLAG_LSIT: {
        key: string;
        value: string;
    }[];
};
export default DICT_CONSTANTS;
