/**
 * 按钮权限常量
 */
declare const PERMISSION_CONSTANTS: {
    TRADING_TYPE: {
        DETAIL: string;
        EDIT: string;
        CREATE: string;
    };
    CONTROL_UNIT: {
        DETAIL: string;
        EDIT: string;
        CREATE: string;
    };
    LIMIT_NODE: {
        DETAIL: string;
        COPY: string;
        EDIT: string;
    };
    RULES_LIMIT: {
        DETAIL: string;
        COPY: string;
        EDIT: string;
        CREATE: string;
    };
    RULES_AUTH: {
        DETAIL: string;
        COPY: string;
        EDIT: string;
        CREATE: string;
    };
    HIGH_RISK_NATION: {
        DETAIL: string;
        EDIT: string;
        CREATE: string;
    };
    PARK_EQUITY: {
        DETAIL: string;
        EDIT: string;
        CREATE: string;
    };
    CARD_HOLDER_BLOCK_CODE: {
        DETAIL: string;
        EDIT: string;
        CREATE: string;
    };
    ACCOUNT_BLOCK_CODE: {
        DETAIL: string;
        EDIT: string;
        CREATE: string;
    };
    CARD_BLOCK_CODE: {
        DETAIL: string;
        EDIT: string;
        CREATE: string;
    };
    AUTH_ORIZATION_RES_CODE: {
        DETAIL: string;
        EDIT: string;
        CREATE: string;
    };
    AUTH_ORIZATION_CONTROL: {
        DETAIL: string;
        EDIT: string;
        CREATE: string;
    };
    SEQUENCE_CODE: {
        DETAIL: string;
        EDIT: string;
        CREATE: string;
    };
    MCC_GROUP_CONTROL: {
        DETAIL: string;
        EDIT: string;
        CREATE: string;
    };
    DIVIDEND_DISCOUNT_GROUP: {
        DETAIL: string;
        EDIT: string;
        CREATE: string;
    };
    RISK_SPECIAL_PARAM: {
        CREATE: string;
        EDIT: string;
        DELETE: string;
    };
    AUTH_POINT_MERCHANT_PARAM: {
        CREATE: string;
        EDIT: string;
    };
    BASE_SYS_PARAM: {
        EDIT: string;
    };
    INFORMATION_INQUIRY: {
        EDIT: string;
    };
    AUTH_ETORO_MERCHANT_PARAM: {
        EDIT: string;
        CREATE: string;
    };
    AUTH_RISK_CTRL_PARAM: {
        EDIT: string;
        CREATE: string;
    };
    BASE_WORK_DAY_PARAM: {
        DETAIL: string;
        EDIT: string;
        CREATE: string;
    };
    LIMIT_HISTORY: {
        DETAIL: string;
    };
    LETTERS_FLOW: {
        DETAIL: string;
    };
    BASE_HOLIDAY_PARAM: {
        DETAIL: string;
        EDIT: string;
        CREATE: string;
    };
    TRAFFIC_CONTROL_PARAM: {
        EDIT: string;
        CREATE: string;
    };
    CARD_QUERY: {
        DETAIL: string;
    };
    TRX_GATEWAY_VISA: {
        UPDATE: string;
    };
    TRX_GATEWAY_MASTERCARD: {
        UPDATE: string;
    };
    TRX_GATEWAY_NCCC: {
        UPDATE: string;
    };
    TRX_GATEWAY_CARDPOOL: {
        UPDATE: string;
    };
    TRX_GATEWAY_CHANELSTATUS: {
        LIST: string;
    };
    EXPORT_GATEWAY_SFMWEB: {
        UPDATE: string;
    };
    EXPORT_GATEWAY_CHANELSTATUS: {
        LIST: string;
    };
    RULE_FACTOR: {
        DETAIL: string;
        EDIT: string;
        CREATE: string;
    };
    AUTHORIZED_TRAN_PARAM: {
        DETAIL: string;
        EDIT: string;
    };
    SYSTEM_MENU: {
        DETAIL: string;
        EDIT: string;
    };
    AUTH_TRANSACTION_CHECK: {
        DETAIL: string;
        EDIT: string;
    };
    CARD_PRODUCT: {
        DETAIL: string;
        EDIT: string;
        CREATE: string;
    };
    ROLE_MANAGE: {
        CREATE: string;
        EDIT: string;
        MOUNT_DETAIL: string;
        MOUNT_EDIT: string;
    };
    DICT_MANAGE: {
        CREATE: string;
        EDIT: string;
        DETAIL: string;
        MAINTAIN: string;
    };
    BLOCK_CODE_MAINTAIN: {
        DETAIL: string;
        EDIT: string;
    };
};
export default PERMISSION_CONSTANTS;
