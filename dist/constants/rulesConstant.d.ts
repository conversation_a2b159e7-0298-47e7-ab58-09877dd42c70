/**
 * 规则模块枚举
 */
export declare const RULE_TYPE: {
    limit: string;
    auth: string;
};
/**
 * 计算表达式运算符枚举
 */
export declare const OPEXP_TYPE: {
    MIN: string;
    MAX: string;
    SUM: string;
};
/**
 * 使用mathjs解析的类型枚举
 */
export declare const MATH_NODE_TYPE: {
    OperatorNode: string;
    FunctionNode: string;
    ConstantNode: string;
};
/**
 * 规则配置类型枚举
 */
export declare const RULE_OPTION_TYPE: {
    judgeExps: string;
    opExps: string;
    ruleResultField: string;
};
/**
 * 规则配置类型
 */
export declare const RULE_OPTION_LIST: {
    opExps: string;
    judgeExps: string;
    ruleResultField: string;
};
/**
 * 判断表达式节点枚举
 */
export declare const RULE_COND_NODE_TYPE: {
    Condition: string;
    ConditionGroup: string;
};
/**
 * 判断表达式条件组枚举
 */
export declare const RULE_COND_NODE_LIST: {
    Condition: number;
    ConditionGroup: number;
};
/**
 * 条件/条件组枚举
 */
export declare const RULE_GROUP_TYPE: {
    ALL: string;
    ONE: string;
};
/**
 * 条件菜单枚举
 */
export declare const COND_MENU_TYPE: {
    addCond: string;
    addCondGroup: string;
    delete: string;
};
/**
 * 规则因子类型枚举
 */
export declare const RULE_FAC_LIST: {
    INTEGER: string;
    LONGINTEGER: string;
    FLOAT: string;
    AMOUNT: string;
    STRING: string;
    PARAMETER: string;
    DICTIONARY: string;
    ARRAY: string;
    BOOLEAN: string;
};
/**
 * 规则里规则因子类型枚举
 */
export declare const RULE_FAC_TYPE: {
    INTEGER: string;
    LONGINTEGER: string;
    FLOAT: string;
    AMOUNT: string;
    STRING: string;
    PARAMETER: string;
    DICTIONARY: string;
    ARRAY: string;
    BOOLEAN: string;
};
/**
 * 规则运算符枚举
 */
export declare const RULE_FAC_OP_TYPE: {
    EQ: string;
    GT: string;
    LT: string;
    NEQ: string;
    GE: string;
    LE: string;
    IN: string;
    HASVAL: string;
    NOHASVAL: string;
    NULL: string;
    NONULL: string;
    BETWEEN: string;
    LIKE: string;
    NOLIKE: string;
    STARTSWITH: string;
    NOTSTARTSWITH: string;
    ENDSWITH: string;
    INLIST: string;
    NOTINLIST: string;
    NOTIN: string;
    CUTEQ: string;
    CUTNOTEQ: string;
    CUTIN: string;
    DIVIDE: string;
    NOTDIVIDE: string;
    NOTEMPTY: string;
    EQIGNORECASE: string;
};
/**
 * 规则布尔型枚举
 */
export declare const RULE_BOOLEAN_TYPE: {
    true: string;
    false: string;
};
/**
 * 规则因子字符型枚举
 */
export declare const RULE_CHAR_LIST: {
    UPPER_CASE_EN: string;
    LOWER_CASE_EN: string;
    SPECIAL_CHAR: string;
    CHINESE: string;
    NUMBER: string;
};
/**
 * 处理错误格式
 */
export declare const RULE_ERROR_TYPE: {
    valueNull: string;
    checkCharFormat: string;
    opExpsValueNull: string;
    opExpsFuncNotMeet: string;
    judgeCondGroupNotMeet: string;
    judgeCondEditing: string;
};
/**
 * 条件表达式中数值类型的规则因子可以选择的运算表达式枚举
 */
export declare const RULE_NUMBER_OPEXPS_LIST: {
    key: string;
    value: string;
    label: string;
    valueWorldSegType: string;
    wordSegBid: string;
}[];
/**
 * 条件中获取的值菜单类型
 */
export declare const RULE_COND_MENU_TYPE: {
    opExps: string;
    paramter: string;
    dictionary: string;
};
/**
 * 运算表达式菜单
 */
export declare const OPEXPS_MENU_TYPE: {
    ruleFacField: string;
    number: string;
    function: string;
    delete: string;
};
