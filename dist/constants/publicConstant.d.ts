/**
 * 默认每页条数
 */
export declare const PAGE_SIZE = 50;
/**
 * 分页每页条数
 */
export declare const PAGE_SIZE_OPTIONS: Array<number>;
/**
 * 无数据展示
 */
export declare const NODATA = "- -";
/**
 * 日期（年月）格式化常量
 */
export declare const MONTH_FORMATE = "YYYY-MM";
/**
 * 日期格式化常量
 */
export declare const DATE_FORMATE = "YYYY-MM-DD";
/**
 * 日期格式化常量
 */
export declare const DATE_FORMATE_SUP = "YYYYMMDD";
/**
 * 时间格式化常量
 */
export declare const TIME_FORMATE = "YYYY-MM-DD HH:mm:ss";
/**
 * 时间格式化常量（只展示时分秒）
 */
export declare const TIME_FORMATE_ONLY = "HHmmss";
/**
 * 时间格式化常量（只展示时分秒）常规
 */
export declare const TIME_FORMATE_ONLY_ONE = "HH:mm:ss";
/**
 * 头部和左侧菜单主题，实际被颜色主题覆盖
 */
export declare const MENU_THEMEN = "dark";
/**
 * 响应成功码
 */
export declare const SUCCESS_CODE = 200;
/**
 * token过期，认证失败码
 */
export declare const AUTH_FAILED = "B1300002";
/**
 * 菜单类型枚举
 */
export declare const MENUTYPE_KEY: {
    directory: string;
    link: string;
    blank: string;
    api: string;
};
/**
 * 操作类型
 */
export declare const OPERATE_TYPE: {
    create: string;
    detail: string;
    copy: string;
    edit: string;
    delete: string;
    list: string;
    cancel: string;
    save: string;
    up: string;
    down: string;
    submit: string;
    flowNode: string;
    dispatch: string;
    dowload: string;
    aiHelp: string;
    menuAuth: string;
    userDetail: string;
    fieldManage: string;
};
/**
 * sessionStorage枚举
 */
export declare const SESSION: {
    token: string;
    codeIndex: string;
    userInfo: string;
    menuData: string;
    menuSelected: string;
};
/**
 * localStorage枚举
 */
export declare const LOCAL: {
    LOCALE: string;
    THEME: string;
    USER_PERMISSION: string;
};
/**
 * 语言枚举
 */
export declare const LANGUAGE_LIST: {
    TW: {
        label: string;
        locale: string;
    };
    CN: {
        label: string;
        locale: string;
    };
    EN: {
        label: string;
        locale: string;
    };
};
/**
 * 常用组件名称枚举
 */
export declare const COMPONENT_TYPE: {
    INPUT: string;
    EXTARA_BTN_INPUT: string;
    INTERVAL_INPUT: string;
    INPUT_NUMBER: string;
    AMOUNT_INPUT: string;
    CHECK_BOX: string;
    SELECT: string;
    RADIO: string;
    TREE: string;
    TREE_SELECT: string;
    CASCADER: string;
    DATE_PICKER: string;
    RANGE_PICKER: string;
    TIME_RANGE_PICKER: string;
    SWITCH: string;
    RATE: string;
    SLIDER: string;
    UPLOAD: string;
    TABLE: string;
    FORM: string;
    EDITTABLE: string;
    TEXTAREA: string;
    DROPDOWN: string;
    SEARCH: string;
};
/**
 * 渲染需要二次处理的类型
 */
export declare const RENDER_TYPE: {
    Amount: string;
    Dictionary: string;
    MockDictionary: string;
    Ellipsis: string;
    DateTime: string;
    Date: string;
};
/**
 * 列表子项类型
 */
export declare const FORMITEM_TYPE: {
    FormHearder: string;
    Single: string;
    Row: string;
    List: string;
    CommonTable: string;
    EditTable: string;
    Calendar: string;
    ParamTable: string;
};
/**
 * 通知类型
 */
export declare const NOTIFICATION_TYPE: {
    SUCCESS: string;
    INFO: string;
    ERROR: string;
    WARNING: string;
};
/**
 * 表格类型
 */
export declare const TABLE_TYPE: {
    CommonTable: string;
    TreeTable: string;
};
/**
 * 提交表单类型
 */
export declare const SUBMIT_TYPE: {
    create: string;
    edit: string;
    delete: string;
    copy: string;
};
/**
 * 提交表单类型
 */
export declare const NOTIFICATION_PROMPT: {
    create: string;
    edit: string;
    delete: string;
};
/**
 * dayjs日期类型
 */
export declare const DATE_TYPE: {
    day: string;
    week: string;
    month: string;
    year: string;
};
/**
 * 参数接口增删改差名称（临时使用，后期后管出接口了在拿掉）
 */
export declare const PARAMS_URLOBJ: {
    list: string;
    chekc: string;
    edit: string;
};
/**
 * 金额常量，默认13位整数，2位小数
 */
export declare const DEFAULT_AMOUNT_PROPS: {
    min: number;
    max: number;
    decimal: number;
    step: number;
    precision: number;
};
/**
 * 请求类型
 */
export declare const REQUEST_TYPE: {
    mock: string;
    business: string;
    param: string;
};
/**
 * EditTable可编辑表格的valueType展示类型
 */
export declare const EDITTABLE_VALUE_TYPE: {
    TEXT: string;
    DIGIT: string;
    SELECT: string;
    TIME: string;
    DATE: string;
};
/**
 * 表单提交错误提示
 */
export declare const FORM_ERROR_CODE: {
    CHECK_MSG: string;
    EDITING: string;
};
/**
 * 默认分页参数
 */
export declare const DEFAULT_PAGINATION: {
    currentPage: number;
    pageSize: number;
};
/**
 * 页面国际化常量
 */
export declare const I18N_COMON_PAGENAME: {
    COMMON: string;
    CALL_PARAM: string;
    SYSTEM_MANAGE: string;
    AUTH_MODULE: string;
    LIMIT: string;
    LIMIT_PARAM: string;
    RULES: string;
    PUBLIC_PARAM: string;
    CARD_PARAM: string;
    SYSTEM: string;
    CARD_MODELE: string;
    API_GATEWAY: string;
    LOGIN: string;
    STRATEGY: string;
    DATA_MANAGE: string;
};
/**
 * 不属于组件的属性常量
 */
export declare const NOT_BELONG_COMPONENT: {
    TYPE: string;
    VALUE: string;
    LABEL: string;
    RULES: string;
    DATA: string;
    SHOWKEY: string;
    REF: string;
    BIND: string;
    PREFIX: string;
    HIDE: string;
    DEPENDENCIES: string;
    DICTtYPE: string;
    FORMLAYOUTTYPE: string;
    COL_SPAN: string;
    PARSER: string;
    SHOULD_UPDATE: string;
    SET_VISIBLE_FUNC: string;
};
/**
 * 表单项渲染布局类型
 */
export declare const FORM_LAYOUT_TYPE: {
    ONE: string;
};
