/**
 * 菜单类型
 */
export declare const MENU_TYPE: {
    COPY: string;
    PASTE: string;
    EDIT: string;
    DELETE: string;
};
/**
 * 元素类型
 */
export declare const ELE_TYPE: {
    NODE: string;
    EDGE: string;
    CANVAS: string;
};
/**
 * 节点类型（可参考G6节点类型扩展）
 */
export declare const NODE_TYPE: {
    IMAGE: string;
    CIRCLE: string;
    RECT: string;
    DIAMOND: string;
    ELLIPSE: string;
    TRIANGLE: string;
};
/**
 * 边类型（可参考G6线类型扩展）
 */
export declare const EDGE_TYPE: {
    POLYlINE: string;
    LINE: string;
    CUBIC_VERTICAL: string;
};
/**
 * 连接桩类型
 */
export declare const PORTS_TYPE: {
    LEFT: string;
    RIGHT: string;
    TOP: string;
    BOTTOM: string;
};
/**
 * 布局类型（可参考G6布局类型扩展）
 */
export declare const LAYOUT_TYPE: {
    ANTV_DARGE: string;
};
