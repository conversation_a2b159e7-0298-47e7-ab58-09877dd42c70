/**
 * 接口常量
 */
declare const URL_CONSTANST: {
    BASE_ORG_PARAM: {
        LIST: string;
    };
    RULE_FACTOR: {
        LIST: string;
        EDIT: string;
        CREATE: string;
    };
    RULES_LIMIT: {
        LIST: string;
        EDIT: string;
        CREATE: string;
    };
    RULES_AUTH: {
        LIST: string;
        EDIT: string;
        CREATE: string;
    };
    RULES_FACTOR: {
        LIST_BY_RULE_TYPE: string;
    };
    CUTTOMER_MANAGE: {
        LIST: string;
        EDIT: string;
        CREATE: string;
    };
    LABEL_MANAGE: {
        LIST: string;
        EDIT: string;
        CREATE: string;
        DELETE: string;
    };
    NODE_MANAGE: {
        LIST: string;
        EDIT: string;
        CREATE: string;
    };
    NODE_FOW_MANAGE: {
        LIST: string;
        EDIT: string;
        CREATE: string;
    };
    PROGRAM_MANAGE: {
        LIST: string;
        EDIT: string;
        CREATE: string;
        DELETE: string;
    };
    CASE_BASE_INFO: {
        LIST: string;
        EDIT: string;
        FLOW_NODE: string;
        ACTION_DETAIL: string;
    };
    ADUIT_MANAGE: {
        LIST: string;
        EDIT: string;
        CREATE: string;
        DELETE: string;
    };
    WORKBENCH_CONFIG: {
        LIST: string;
        EDIT: string;
        CREATE: string;
    };
    CARD_QUERY: {
        LIST: string;
        DETAIL: string;
    };
    USER_MANAGEMENT: {
        LIST: string;
        EDIT: string;
        CREATE: string;
        DELETE: string;
    };
    POSITION_MANAGEMENT: {
        LIST: string;
        EDIT: string;
        CREATE: string;
        DELETE: string;
    };
    ORGANIZATION_MANAGE: {
        LIST: string;
        EDIT: string;
        CREATE: string;
        DELETE: string;
    };
    MENU_MANAGE: {
        LIST: string;
        EDIT: string;
        DELETE: string;
    };
    ROLE_MANAGE: {
        LIST: string;
        EDIT: string;
        CREATE: string;
        DELETE: string;
    };
    DICT_MANAGE: {
        LIST: string;
        EDIT: string;
        CREATE: string;
        DELETE: string;
    };
};
export default URL_CONSTANST;
