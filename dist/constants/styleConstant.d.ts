/**
 * 样式常量
 */
declare const _default: {
    width100: {
        width: string;
    };
    width80: {
        width: string;
    };
    width60: {
        width: string;
    };
    width50: {
        width: string;
    };
    width40: {
        width: string;
    };
    width20: {
        width: string;
    };
    height100: {
        height: string;
    };
    height80: {
        height: string;
    };
    height60: {
        height: string;
    };
    height50: {
        height: string;
    };
    height40: {
        height: string;
    };
    height20: {
        height: string;
    };
    iconSize: {
        fontSize: number;
    };
    mt: {
        marginTop: string;
    };
    mb: {
        marginBottom: string;
    };
    mb2: {
        marginBottom: string;
    };
    ml: {
        marginLeft: string;
    };
    mr: {
        marginRight: string;
    };
    mtb: {
        marginTop: string;
        marginBottom: string;
    };
    mlr: {
        marginLeft: string;
        marginRight: string;
    };
    mts: {
        marginTop: string;
    };
    mbs: {
        marginBottom: string;
    };
    mls: {
        marginLeft: string;
    };
    mrs: {
        marginRight: string;
    };
    mtbs: {
        marginTop: string;
        marginBottom: string;
    };
    mlrs: {
        marginLeft: string;
        marginRight: string;
    };
    pt: {
        paddingTop: string;
    };
    pb: {
        paddingBottom: string;
    };
    pb2: {
        paddingBottom: string;
    };
    pl: {
        paddingLeft: string;
    };
    pr: {
        paddingRight: string;
    };
    ptb: {
        paddingTop: string;
        paddingBottom: string;
    };
    plr: {
        paddingLeft: string;
        paddingRight: string;
    };
    pts: {
        paddingTop: string;
    };
    pbs: {
        paddingBottom: string;
    };
    pls: {
        paddingLeft: string;
    };
    prs: {
        paddingRight: string;
    };
    ptbs: {
        paddingTop: string;
        paddingBottom: string;
    };
    plrs: {
        paddingLeft: string;
        paddingRight: string;
    };
    grayLight: string;
    grayOne: string;
    grayTwo: string;
    grayThree: string;
    grayMain: string;
    grayFive: string;
    graySix: string;
    warningColor: string;
    errorColor: string;
    successColor: string;
    formLayout: {
        labelCol: {
            span: number;
        };
        wrapperCol: {
            span: number;
        };
    };
    formLayout2: {
        labelCol: {
            span: number;
        };
        wrapperCol: {
            span: number;
        };
    };
    formLayout3: {
        labelCol: {
            span: number;
        };
        wrapperCol: {
            span: number;
        };
    };
    formLayout4: {
        labelCol: {
            span: number;
        };
        wrapperCol: {
            span: number;
        };
    };
};
export default _default;
declare const greenTheme: {
    colorOne: string;
    colorTwo: string;
    colorThree: string;
    colorMain: string;
    colorFive: string;
    colorSix: string;
};
declare const blueTheme: {
    colorOne: string;
    colorTwo: string;
    colorThree: string;
    colorMain: string;
    colorFive: string;
    colorSix: string;
};
declare const redTheme: {
    colorOne: string;
    colorTwo: string;
    colorThree: string;
    colorMain: string;
    colorFive: string;
    colorSix: string;
};
declare const themeColor: {
    green: string;
    blue: string;
};
declare const themeMap: {
    '#00994e': {
        colorOne: string;
        colorTwo: string;
        colorThree: string;
        colorMain: string;
        colorFive: string;
        colorSix: string;
    };
    '#345da7': {
        colorOne: string;
        colorTwo: string;
        colorThree: string;
        colorMain: string;
        colorFive: string;
        colorSix: string;
    };
    '#f5222d': {
        colorOne: string;
        colorTwo: string;
        colorThree: string;
        colorMain: string;
        colorFive: string;
        colorSix: string;
    };
    '#282a36': {
        colorOne: string;
        colorTwo: string;
        colorThree: string;
        colorMain: string;
        colorFive: string;
        colorSix: string;
    };
};
declare const themeGradientButton: {
    [themeColor.green]: string[];
    [themeColor.blue]: string[];
    white: string[];
};
export { greenTheme, blueTheme, redTheme, themeColor, themeMap, themeGradientButton };
