interface UserState {
    initDone: boolean;
    permissionButtons: Array<{
        permissionCode: string;
    }>;
}
interface DictState {
    dictMap: {
        [key: string]: any;
    };
}
interface DictDispatchers {
    getDictList: (params: any) => Promise<void>;
    updateDictMap: (data: any) => void;
}
declare const store: {
    useModel: (modelName: string) => UserState[] | (DictState | DictDispatchers)[] | {}[];
};
export default store;
