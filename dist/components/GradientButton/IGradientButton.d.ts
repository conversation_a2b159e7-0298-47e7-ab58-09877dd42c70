import { default as React } from 'react';
import { ButtonType } from 'antd/es/button';
import { SizeType } from 'antd/es/config-provider/SizeContext';

export interface IButtonProps {
    children?: React.ReactNode;
    color?: string;
    type?: ButtonType;
    size?: SizeType;
    icon?: any | undefined;
    style?: object | undefined;
    disabled?: boolean;
    loading?: boolean;
    onClick?: () => void;
}
