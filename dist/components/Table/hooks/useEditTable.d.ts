import { IEditableColumns } from '../../../types/IForm';

declare const useEditTable: ({ intlPrefix, rowKey, optionCount, orderFiled, editableKeys, tableData, setTableData, getOptionList, onAction, }: {
    intlPrefix: any;
    rowKey: any;
    optionCount: any;
    orderFiled: any;
    editableKeys: any;
    tableData: any;
    setTableData: any;
    getOptionList: any;
    onAction: any;
}) => {
    getColumns: (columns: any) => IEditableColumns[];
};
export default useEditTable;
