import { ISearchSource } from '../../types/ICommon';
import { default as React } from 'react';

export interface ISearchProps {
    children?: React.ReactNode;
    searchTitle?: string;
    resetTitle?: string;
    createTitle?: string;
    searchValue: object;
    resetValue: object;
    searchSource: Array<ISearchSource>;
    intlPrefix?: string;
    buttonDisabled?: boolean;
    onSearch: (obj: object) => void;
    onCreate?: () => void;
    onChange?: (changedValues: object, allValues: object) => void;
    onRest?: (searchSource?: object) => void;
}
