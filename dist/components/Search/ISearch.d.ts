import { ISearchSource } from '../../types/ICommon';
import { default as React } from 'react';

export interface ISearchProps {
    children?: React.ReactNode;
    searchTitle?: string;
    resetTitle?: string;
    createTitle?: string;
    searchValue: Record<string, any>;
    resetValue: Record<string, any>;
    searchSource: Array<ISearchSource>;
    intlPrefix?: string;
    buttonDisabled?: boolean;
    onSearch: (values: Record<string, any>) => void;
    onCreate?: () => void;
    onChange?: (changedValues: Record<string, any>, allValues: Record<string, any>) => void;
    onRest?: (searchSource?: Record<string, any>) => void;
}
