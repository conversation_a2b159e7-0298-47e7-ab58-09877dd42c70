import { ICommonTableActionItem } from '../types/ICommon';
import { TNotification, TOptionItem } from '../types/TCommon';
import { ReactNode } from 'react';

export declare const getCurrentLocale: () => string;
export declare const intlConst: {
    formatMessage: (prefix: string, id: string, params?: Record<string, any>) => string;
    formatDate: (date: Date, options?: Intl.DateTimeFormatOptions) => string;
    formatNumber: (value: number, options?: Intl.NumberFormatOptions) => string;
};
declare const useIntlCustom: () => {
    translate: (prefix: string, key: string, params?: Record<string, any>) => string;
    formatActionTitle: (actionType: string, prefix: string, title?: string) => string;
    formateHtmlText: (prefix: string, key: string) => React.ReactNode;
    formatLocalDate: (date: any, options: any) => string;
    formatLocalNumber: (value: any, options: any) => string;
    getSelectOption: (data: Array<TOptionItem>, showKey?: boolean, prefix?: string) => TOptionItem[];
    getEditTableSelectOption: (data: Array<TOptionItem>, showKey?: boolean, prefix?: string) => object;
    getActionResultTitle: (title: string, prefix: string, type: string, isSuccess: boolean) => string;
    openNotificationTip: (prefix: string, type: TNotification | string, context: string, duration?: number) => void;
    defaultInputPlaceholder: string;
    defaultSelectPlaceholder: string;
    renderActionColumn: (data?: Array<string | ICommonTableActionItem>, onClick?: (type: any) => void) => ReactNode | null;
};
export default useIntlCustom;
