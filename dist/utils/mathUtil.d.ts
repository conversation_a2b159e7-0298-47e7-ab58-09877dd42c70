/**
 * 数字格式化为千分位
 * @param number 需要格式化的数字
 * @param decimal 保留小数位
 * @returns {number}
 */
export declare const formatNumber: (number: number, decimal?: number) => string | 0;
/**
 * 判空（除了数字0和false）
 * @param value 需要判断的值
 * @returns {boolean}
 */
export declare const isEmpty: (value: any) => boolean;
/**
 * 10进制转成64进制
 * @param num
 * @returns {string}
 */
export declare const decimalToBase64: (num: number) => string;
