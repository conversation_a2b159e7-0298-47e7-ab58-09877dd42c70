import { ReactNode } from 'react';

/**
 * 获取表格操列宽度
 * @param count 操作按钮数量
 * @returns {number}
 */
export declare function getActionColumnWidth(count?: number): number;
/**
 * 递归查找树形结构中的根节点
 * @param nodes 节点数组
 * @param targetId 目标节点ID
 * @param id ID字段名
 * @param parentId 父ID字段名
 * @returns {object}
 */
export declare const findRootNode: (nodes: any[], targetId: any, id: string, parentId: string) => object;
/**
 * 将扁平化数据转为树形结构
 * @param flatData 扁平化数据
 * @param id id的key
 * @param parentId 父级id的key
 * @returns {Array}
 */
export declare const buildTree: (flatData: any[], id: string, parentId: string) => any[];
/**
 * 扁平化对象
 * @param obj 对象
 * @param parentKey 父级key
 * @param result 上一次遍历的结果
 * @returns {object}
 */
export declare const flattenObject: (obj: object, parentKey?: string, result?: {}) => object;
/**
 * 嵌套对象扁平化
 * @param obj
 * @returns {object} 模块扁平化对象
 */
export declare const flattenNestedObject: (obj: object) => object;
/**
 * 找出目录下符合规则的文件
 * @param context 上下文
 * @param reg 匹配文件的正则
 * @param flatten 输出对象是否需要扁平化 {模块1: {字段1，字段2}, 模块2: {字段3, 字段4}}
 * @returns {object}
 */
export declare const getFileObj: (context: any, reg: RegExp, flatten?: boolean) => object;
/**
 * 根据传入的rem转换为px某些组件如G6需要使用px）
 * @param rem
 * @returns {string}
 */
export declare const getFontSize: (rem: any) => number;
/**
 * 动态修改表单某项的disabled的值，支持多项修改
 * @param infoFormConfig 表单内容
 * @param fieldName 需要修改的字段，支持'a,b,c'传输
 * @param newDisabledValue 修改后的属性
 */
export declare const updateDisabledProperty: (infoFormConfig: any, fieldName: any, newDisabledValue: any) => void;
/**
 * 动态修改或新增表单某个或多个项的某个属性的值
 * @param infoFormConfig 表单内容
 * @param fieldName 需要修改的字段，支持'a,b,c'传输
 * @param updatePropName 对应fieldName下想要修改的属性名
 * @param newValue 修改后的值
 */
export declare const updateProperty: (infoFormConfig: any, fieldName: any, updatePropName: string, newValue: any) => void;
/**
 * 格式化值
 * @param value 值
 * @param option 相关配置（如valueType、prefix...）
 * @returns {string | JSX}
 */
export declare const renderValueByType: (value: any, option: any) => any;
/**
 * 格式化列表值
 * @param column 每列列表数据
 * @returns {object}
 */
export declare const renderColumnByType: (column: any) => any;
/**
 * 是否请求列表接口，查询条件若有必输，首次进页面则不掉用接口
 * @param data 查询条件数据
 * @returns
 */
export declare const isGetTableData: (data: any) => any;
/**
 * 格式化form时间
 * @param type 字段类型
 * @param value 字段值
 * @returns
 */
export declare const formatformItemData: (type: any, value: any) => any;
/**
 * 处理form录入的值，时间格式或者输入框去掉空格
 * @param type 字段类型
 * @param value 字段值
 * @param format 传入的格式，用于格式化时间
 * @returns
 */
export declare const formatformItemNormalize: (type: any, value: any, format?: string) => any;
export declare const formatterAmountInput: (value: any, decimal: any) => string;
/**
 *  处理金额数据
 * @param value 输入框的值
 * @returns
 */
export declare const parserAmountInput: (value: any) => number;
export declare const renderButton: (data: any) => ReactNode;
/**
 * 分页国际化
 * @param total 总页数
 * @returns {string}
 */
export declare const showTotal: (total: number) => string;
