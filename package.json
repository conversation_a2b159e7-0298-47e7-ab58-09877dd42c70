{"name": "my-custom-ui-components", "private": false, "version": "0.0.1", "type": "module", "main": "./dist/my-custom-ui-components.umd.js", "module": "./dist/my-custom-ui-components.es.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/my-custom-ui-components.es.js", "require": "./dist/my-custom-ui-components.umd.js"}, "./dist/style.css": "./dist/style.css"}, "files": ["dist"], "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "prepare": "npm run build"}, "dependencies": {"@ant-design/pro-components": "^2.4.0", "@types/node": "^24.3.1", "antd": "^5.18.3", "dayjs": "^1.11.18", "lodash": "^4.17.21", "react": ">=18.0.0", "react-dom": ">=18.0.0", "react-intl": "^6.8.9"}, "devDependencies": {"@types/lodash": "^4.17.6", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.2.1", "echarts": "^5.5.1", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "typescript": "^5.2.2", "vite": "^5.2.0", "vite-plugin-dts": "^3.9.1"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}}