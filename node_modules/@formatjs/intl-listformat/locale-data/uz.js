/* @generated */
// prettier-ignore
if (Intl.ListFormat && typeof Intl.ListFormat.__addLocaleData === 'function') {
  Intl.ListFormat.__addLocaleData({
  "data": {
    "conjunction": {
      "long": {
        "end": "{0} va {1}",
        "middle": "{0}, {1}",
        "pair": "{0} va {1}",
        "start": "{0}, {1}"
      },
      "narrow": {
        "end": "{0}, {1}",
        "middle": "{0}, {1}",
        "pair": "{0}, {1}",
        "start": "{0}, {1}"
      },
      "short": {
        "end": "{0} va {1}",
        "middle": "{0}, {1}",
        "pair": "{0} va {1}",
        "start": "{0}, {1}"
      }
    },
    "disjunction": {
      "long": {
        "end": "{0} yoki {1}",
        "middle": "{0}, {1}",
        "pair": "{0} yoki {1}",
        "start": "{0}, {1}"
      },
      "narrow": {
        "end": "{0} yoki {1}",
        "middle": "{0}, {1}",
        "pair": "{0} yoki {1}",
        "start": "{0}, {1}"
      },
      "short": {
        "end": "{0} yoki {1}",
        "middle": "{0}, {1}",
        "pair": "{0} yoki {1}",
        "start": "{0}, {1}"
      }
    },
    "unit": {
      "long": {
        "end": "{0} {1}",
        "middle": "{0} {1}",
        "pair": "{0} {1}",
        "start": "{0} {1}"
      },
      "narrow": {
        "end": "{0} {1}",
        "middle": "{0} {1}",
        "pair": "{0} {1}",
        "start": "{0} {1}"
      },
      "short": {
        "end": "{0} {1}",
        "middle": "{0} {1}",
        "pair": "{0} {1}",
        "start": "{0} {1}"
      }
    }
  },
  "locale": "uz"
})
}