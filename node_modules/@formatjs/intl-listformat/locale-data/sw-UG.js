/* @generated */
// prettier-ignore
if (Intl.ListFormat && typeof Intl.ListFormat.__addLocaleData === 'function') {
  Intl.ListFormat.__addLocaleData({
  "data": {
    "conjunction": {
      "long": {
        "end": "{0} na {1}",
        "middle": "{0}, {1}",
        "pair": "{0} na {1}",
        "start": "{0}, {1}"
      },
      "narrow": {
        "end": "{0} na {1}",
        "middle": "{0}, {1}",
        "pair": "{0}, {1}",
        "start": "{0}, {1}"
      },
      "short": {
        "end": "{0} na {1}",
        "middle": "{0}, {1}",
        "pair": "{0} na {1}",
        "start": "{0}, {1}"
      }
    },
    "disjunction": {
      "long": {
        "end": "{0} au {1}",
        "middle": "{0}, {1}",
        "pair": "{0} au {1}",
        "start": "{0}, {1}"
      },
      "narrow": {
        "end": "{0}, au {1}",
        "middle": "{0}, {1}",
        "pair": "{0} au {1}",
        "start": "{0}, {1}"
      },
      "short": {
        "end": "{0}, au {1}",
        "middle": "{0}, {1}",
        "pair": "{0} au {1}",
        "start": "{0}, {1}"
      }
    },
    "unit": {
      "long": {
        "end": "{0} na {1}",
        "middle": "{0}, {1}",
        "pair": "{0} na {1}",
        "start": "{0}, {1}"
      },
      "narrow": {
        "end": "{0} na {1}",
        "middle": "{0}, {1}",
        "pair": "{0} na {1}",
        "start": "{0}, {1}"
      },
      "short": {
        "end": "{0} na {1}",
        "middle": "{0}, {1}",
        "pair": "{0} na {1}",
        "start": "{0}, {1}"
      }
    }
  },
  "locale": "sw-UG"
})
}