{"name": "@formatjs/intl-listformat", "version": "7.7.5", "description": "Formats JS list in a i18n-safe way", "keywords": ["intl", "i18n", "list", "format", "formatjs", "listformat"], "author": "<PERSON> <<EMAIL>>", "bugs": {"url": "https://github.com/formatjs/formatjs/issues"}, "repository": {"type": "git", "url": "**************:formatjs/formatjs.git"}, "dependencies": {"tslib": "2", "@formatjs/ecma402-abstract": "2.2.4", "@formatjs/intl-localematcher": "0.5.8"}, "devDependencies": {"@formatjs/intl-getcanonicallocales": "2.5.3", "@formatjs/intl-locale": "4.2.5"}, "main": "index.js", "types": "index.d.ts", "homepage": "https://github.com/formatjs/formatjs", "license": "MIT", "gitHead": "a7842673d8ad205171ad7c8cb8bb2f318b427c0c"}