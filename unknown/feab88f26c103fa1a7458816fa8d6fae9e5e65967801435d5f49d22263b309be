/**
 * 按钮权限常量
 */

const PERMISSION_CONSTANTS = {
  // 授权交易类型
  TRADING_TYPE: {
    DETAIL: 'param:auth:tradingType:check',
    EDIT: 'param:auth:tradingType:edit',
    CREATE: 'param:auth:tradingType:create',
  },
  // 管控单元
  CONTROL_UNIT: {
    DETAIL: 'param:limit:controlUnit:check',
    EDIT: 'param:limit:controlUnit:edit',
    CREATE: 'param:limit:controlUnit:creat',
  },
  // 额度节点
  LIMIT_NODE: {
    DETAIL: 'param:limit:limitNode:check',
    COPY: 'param:limit:limitNode:copy',
    EDIT: 'param:limit:limitNode:edit',
  },
  // 规则-额度
  RULES_LIMIT: {
    DETAIL: 'param:rules:rules:limit:check',
    COPY: 'param:rules:rules:limit:copy',
    EDIT: 'param:rules:rules:limit:edit',
    CREATE: 'param:rules:rules:limit:create',
  },
  // 规则-授权
  RULES_AUTH: {
    DETAIL: 'param:rules:rules:auth:check',
    COPY: 'param:rules:rules:auth:copy',
    EDIT: 'param:rules:rules:auth:edit',
    CREATE: 'param:rules:rules:auth:create',
  },
  // 高风险国家参数
  HIGH_RISK_NATION: {
    DETAIL: 'param:auth:highRiskNation:check',
    EDIT: 'param:auth:highRiskNation:edit',
    CREATE: 'param:auth:highRiskNation:create',
  },
  // 停車權益參數
  PARK_EQUITY: {
    DETAIL: 'param:auth:parkEquity:check',
    EDIT: 'param:auth:parkEquity:edit',
    CREATE: 'param:auth:parkEquity:create',
  },
  // 持卡人封鎖碼參數
  CARD_HOLDER_BLOCK_CODE: {
    DETAIL: 'param:auth:cardHolderBlockCode:check',
    EDIT: 'param:auth:cardHolderBlockCode:edit',
    CREATE: 'param:auth:cardHolderBlockCode:create',
  },
  // 帳戶封鎖碼參數
  ACCOUNT_BLOCK_CODE: {
    DETAIL: 'param:auth:accountBlockCode:check',
    EDIT: 'param:auth:accountBlockCode:edit',
    CREATE: 'param:auth:accountBlockCode:create',
  },
  // 卡片封鎖碼參數
  CARD_BLOCK_CODE: {
    DETAIL: 'param:auth:cardBlockCode:check',
    EDIT: 'param:auth:cardBlockCode:edit',
    CREATE: 'param:auth:cardBlockCode:create',
  },
  // 授权响应码参数
  AUTH_ORIZATION_RES_CODE: {
    DETAIL: 'param:auth:authResCode:check',
    EDIT: 'param:auth:authResCode:edit',
    CREATE: 'param:auth:authResCode:creat',
  },
  // 卡产品授权管控参数
  AUTH_ORIZATION_CONTROL: {
    DETAIL: 'param:auth:authControl:check',
    EDIT: 'param:auth:authControl:edit',
    CREATE: 'param:auth:authControl:creat',
  },
  // 授权交易顺序码参数表
  SEQUENCE_CODE: {
    DETAIL: 'param:auth:sequence:check',
    EDIT: 'param:auth:sequence:edit',
    CREATE: 'param:auth:sequence:creat',
  },
  // 卡产品MCC组管控参数
  MCC_GROUP_CONTROL: {
    DETAIL: 'param:authParam:mccGroupControl:check',
    EDIT: 'param:authParam:mccGroupControl:edit',
    CREATE: 'param:authParam:mccGroupControl:create',
  },
  // 红利折抵特店组表
  DIVIDEND_DISCOUNT_GROUP: {
    DETAIL: 'param:authParam:discountGroup:check',
    EDIT: 'param:authParam:discountGroup:edit',
    CREATE: 'param:authParam:discountGroup:create',
  },
  // 风险特店参数表
  RISK_SPECIAL_PARAM: {
    CREATE: 'param:param:authParam:riskSpecialParam:create',
    EDIT: 'param:param:authParam:riskSpecialParam:edit',
    DELETE: 'param:param:authParam:riskSpecialParam:delete',
  },
  // 红利折抵特店表
  AUTH_POINT_MERCHANT_PARAM: {
    CREATE: 'param:authParam:dividendDiscount:create',
    EDIT: 'param:authParam:dividendDiscount:edit',
  },
  // 系统参数表
  BASE_SYS_PARAM: {
    EDIT: 'param:param:publicParam:systemParam:edit',
  },
  // 额度咨询查询
  INFORMATION_INQUIRY: {
    EDIT: 'service:limit:informationInquiry:edit',
  },
  // 境外投资平台特店代号表
  AUTH_ETORO_MERCHANT_PARAM: {
    EDIT: 'param:param:authParam:overseasInvestmentPlatform:edit',
    CREATE: 'param:param:authParam:overseasInvestmentPlatform:create',
  },
  // 授權風險管控參數（OAFX）
  AUTH_RISK_CTRL_PARAM: {
    EDIT: 'param:param:authParam:authRiskControlParams:edit',
    CREATE: 'param:param:authParam:authRiskControlParams:create',
  },

  // 工作日参数
  BASE_WORK_DAY_PARAM: {
    DETAIL: 'param:param:publicParam:weekdayParam:check',
    EDIT: 'param:param:publicParam:weekdayParam:edit',
    CREATE: 'param:param:publicParam:weekdayParam:create',
  },
  // 额度调整历史
  LIMIT_HISTORY: { DETAIL: 'service:limit:limitHistory:check' },
  // 用信流水
  LETTERS_FLOW: { DETAIL: 'service:limit:lettersFlow:check' },
  // 假日参数表
  BASE_HOLIDAY_PARAM: {
    DETAIL: 'param:param:publicParam:holidayParam:check',
    EDIT: 'param:param:publicParam:holidayParam:edit',
    CREATE: 'param:param:publicParam:holidayParam:create',
  },
  // 流量管控参数
  TRAFFIC_CONTROL_PARAM: {
    EDIT: 'param:param:authParam:trafficControlParam:edit',
    CREATE: 'param:param:authParam:trafficControlParam:create',
  },
  // 卡户人查询-卡片查询
  CARD_QUERY: {
    DETAIL: 'service:card:custCardQuery:cardQuery:check',
  },
  // 交易網管-visa
  TRX_GATEWAY_VISA: { UPDATE: 'system:trxGateway:visa:update' },
  // 交易網管-萬事達
  TRX_GATEWAY_MASTERCARD: { UPDATE: 'system:trxGateway:masterCard:update' },
  // 交易網管-nccc
  TRX_GATEWAY_NCCC: { UPDATE: 'system:trxGateway:nccc:update' },
  // 交易網管-cardpool
  TRX_GATEWAY_CARDPOOL: { UPDATE: 'system:trxGateway:cardpool:update' },
  // 交易網管-狀態查詢
  TRX_GATEWAY_CHANELSTATUS: { LIST: 'system:trxGateway:chanelStatus:query' },
  // 出口網管-sfm
  EXPORT_GATEWAY_SFMWEB: { UPDATE: 'system:exportGateway:sfmWebSite:update' },
  // 出口網管-狀態查詢
  EXPORT_GATEWAY_CHANELSTATUS: { LIST: 'system:exportGateway:channelSts:query' },
  // 規則因子
  RULE_FACTOR: {
    DETAIL: 'param:rules:ruleFactor:check',
    EDIT: 'param:rules:ruleFactor:edit',
    CREATE: 'param:rules:ruleFactor:create',
  },
  // 授权交易检查项
  AUTHORIZED_TRAN_PARAM: {
    DETAIL: 'param:param:authParam:authTran:check',
    EDIT: 'param:param:authParam:authTran:edit',
  },
  // 菜单管理
  SYSTEM_MENU: {
    DETAIL: 'system:menu:check',
    EDIT: 'system:menu:edit',
  },
  // 交易檢查項控制
  AUTH_TRANSACTION_CHECK: {
    DETAIL: 'param:auth:authTransControl:check',
    EDIT: 'param:auth:authTransControl:edit',
  },
  // 卡片产品參數
  CARD_PRODUCT: {
    DETAIL: 'param:cardParam:cardProduct:check',
    EDIT: 'param:cardParam:cardProduct:edit',
    CREATE: 'param:cardParam:cardProduct:create',
  },
  // 角色管理
  ROLE_MANAGE: {
    CREATE: 'system:role:create',
    EDIT: 'system:role:edit',
    MOUNT_DETAIL: 'system:role:viewPermissionMounting',
    MOUNT_EDIT: 'system:role:editPermissionMounting',
  },
  DICT_MANAGE: {
    CREATE: 'system:dictonary:create',
    EDIT: 'system:dictonary:edit',
    DETAIL: 'system:dictonary:check',
    MAINTAIN: 'system:dictonary:maintain',
  },
  BLOCK_CODE_MAINTAIN: {
    DETAIL: 'service:card:blockCodeMaintenance:check',
    EDIT: 'service:card:blockCodeMaintenance:edit',
  },
};

export default PERMISSION_CONSTANTS;
